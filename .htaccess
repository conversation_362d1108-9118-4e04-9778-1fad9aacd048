# نظام صرف حصص الوقود الذكي - إعدادات Apache

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# منع الوصول للملفات الحساسة
<Files "*.db">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

# منع الوصول لمجلدات النظام
RedirectMatch 404 /\.git
RedirectMatch 404 /config/
RedirectMatch 404 /database/
RedirectMatch 404 /vendor/

# إعادة توجيه جميع الطلبات إلى index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/(assets|storage)/
RewriteRule ^(.*)$ index.php [QSA,L]

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تحسين التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    ExpiresByType image/icon "access plus 1 year"
    ExpiresByType text/plain "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# إعدادات الأمان
<IfModule mod_headers.c>
    # منع تضمين الموقع في إطارات خارجية
    Header always append X-Frame-Options SAMEORIGIN
    
    # منع تشغيل السكريبت في المتصفح
    Header set X-XSS-Protection "1; mode=block"
    
    # منع تخمين نوع المحتوى
    Header set X-Content-Type-Options nosniff
    
    # إخفاء معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# منع الوصول للملفات المخفية
<Files ".*">
    Order Allow,Deny
    Deny from all
</Files>

# السماح بالوصول لملفات CSS و JS
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# إعدادات PHP
<IfModule mod_php7.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_vars 3000
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log logs/php_errors.log
</IfModule>

# منع الوصول المباشر لملفات PHP في مجلدات معينة
<Directory "models">
    <Files "*.php">
        Order Allow,Deny
        Deny from all
    </Files>
</Directory>

<Directory "controllers">
    <Files "*.php">
        Order Allow,Deny
        Deny from all
    </Files>
</Directory>

# صفحات الأخطاء المخصصة
ErrorDocument 404 /DESL_2/404.html
ErrorDocument 403 /DESL_2/403.html
ErrorDocument 500 /DESL_2/500.html

# تحسين الأداء
<IfModule mod_rewrite.c>
    # إزالة www من الرابط
    RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
    RewriteRule ^(.*)$ http://%1/$1 [R=301,L]
    
    # فرض استخدام HTTPS (في الإنتاج)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>

# منع الوصول لملفات النسخ الاحتياطي
<FilesMatch "\.(bak|backup|old|tmp|temp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# حد معدل الطلبات (إذا كان متاحاً)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSSiteCount        50
    DOSPageInterval     1
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>
