# 🚀 دليل التشغيل السريع - نظام صرف حصص الوقود الذكي

## ✅ تم إصلاح المشكلة!

النظام يعمل الآن بشكل صحيح بعد إصلاح مشكلة ملف .htaccess.

## 🔗 الروابط المباشرة

### 🏠 الصفحة الرئيسية
```
http://localhost/DESL_2
```

### 🔐 تسجيل الدخول
```
http://localhost/DESL_2/login.php
```

### 📊 لوحة التحكم
```
http://localhost/DESL_2/dashboard.php
```

### ⛽ طلب وقود جديد
```
http://localhost/DESL_2/fuel_request.php
```

### 🚗 إدارة المركبات
```
http://localhost/DESL_2/vehicles.php
```

## 👥 بيانات تسجيل الدخول

### 🔧 مدير النظام
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `admin123`
- **الصلاحيات:** جميع المميزات

### 👨‍💼 مشرف
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `supervisor123`
- **الصلاحيات:** إدارة الطلبات والمركبات

### 🏢 جهة طلب (وزارة الصحة)
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `requester123`
- **الصلاحيات:** إنشاء طلبات الوقود

### ⛽ محطة وقود
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `station123`
- **الصلاحيات:** إدارة طلبات المحطة

### 📋 مراجع مركزي
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `reviewer123`
- **الصلاحيات:** مراجعة وتدقيق الطلبات

## 🎯 خطوات الاختبار السريع

### 1️⃣ تسجيل الدخول
1. افتح `http://localhost/DESL_2/login.php`
2. استخدم أي من بيانات تسجيل الدخول أعلاه
3. ستنتقل تلقائياً للوحة التحكم

### 2️⃣ إنشاء طلب وقود
1. من لوحة التحكم، اضغط "طلب وقود جديد"
2. أدخل رقم لوحة موجود مثل: `س و 1234`
3. اختر نوع الوقود والكمية
4. اختر محطة الوقود
5. اضغط "إرسال الطلب"

### 3️⃣ إضافة مركبة جديدة
1. اذهب إلى "إدارة المركبات"
2. املأ بيانات المركبة الجديدة
3. اضغط "إضافة المركبة"

## 📋 البيانات التجريبية الموجودة

### 🚗 المركبات
- `س و 1234` - وزارة الصحة (جازولين)
- `س و 5678` - وزارة التعليم (ديزل)
- `س و 9012` - وزارة الداخلية (ديزل)
- `س و 3456` - وزارة الدفاع (جازولين)
- `س و 7890` - وزارة المياه (ديزل)

### ⛽ المحطات
- محطة الخرطوم المركزية
- محطة بحري الشمالية
- محطة أمدرمان الغربية
- محطة الكلاكلة
- محطة شرق النيل

### 📋 طلبات الوقود
- طلبات معتمدة ومعلقة ومرفوضة للاختبار

## 🔧 المميزات المتاحة

### ✅ تم تنفيذها
- ✅ نظام تسجيل الدخول الآمن
- ✅ لوحة تحكم تفاعلية
- ✅ إنشاء طلبات الوقود
- ✅ إدارة المركبات
- ✅ عرض الإحصائيات
- ✅ قاعدة بيانات SQLite
- ✅ واجهة عربية متجاوبة

### 🚧 قيد التطوير
- 🚧 إدارة المحطات الكاملة
- 🚧 نظام التقارير المتقدم
- 🚧 توليد الخطابات الرسمية
- 🚧 نظام الحصص المتقدم
- 🚧 رموز QR للتحقق

## 🛠️ إعادة تهيئة النظام

إذا كنت تريد إعادة تهيئة قاعدة البيانات:

```
http://localhost/DESL_2/setup.php
```

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تأكد من تشغيل XAMPP
2. تأكد من وجود مجلد `DESL_2` في `C:\xampp\htdocs\`
3. تحقق من سجل أخطاء Apache في `C:\xampp\apache\logs\error.log`

## 🎉 النظام جاهز للاستخدام!

يمكنك الآن استكشاف جميع مميزات النظام والبدء في الاختبار والتطوير.

---
**© 2025 وزارة المالية والتخطيط الاقتصادي - ولاية الخرطوم**
