# نظام صرف حصص الوقود الذكي

## 🛠️ نظرة عامة

نظام صرف حصص الوقود الذكي هو نظام متكامل لإدارة وصرف حصص الوقود للجهات الحكومية في ولاية الخرطوم. يوفر النظام واجهة ويب حديثة مع إمكانيات متقدمة لإدارة المركبات والمحطات وطلبات الوقود.

## ✨ المميزات الرئيسية

### 🔐 نظام المصادقة والصلاحيات
- تسجيل دخول آمن مع أدوار متعددة
- صلاحيات متدرجة (مدير، مشرف، جهة طلب، محطة، مراجع)
- حماية من محاولات تسجيل الدخول المتكررة
- انتهاء صلاحية الجلسة التلقائي

### 🚗 إدارة المركبات
- تسجيل المركبات برقم اللوحة والشاسيه
- ربط المركبات بالجهات المالكة
- تتبع نوع الوقود وسعة الخزان
- نظام حصص ذكي مع تتبع الاستهلاك

### ⛽ إدارة محطات الوقود
- قاعدة بيانات شاملة لمحطات الخرطوم
- معلومات الموقع والإحداثيات الجغرافية
- تصنيف المحطات (حكومية/خاصة)
- إحصائيات نشاط المحطات

### 📋 نظام طلبات الوقود
- واجهة سهلة لإنشاء طلبات الوقود
- التحقق التلقائي من الحصص المتاحة
- منع الطلبات المكررة
- تتبع حالة الطلبات (معلق، معتمد، مرفوض)

### 📄 الخطابات الرسمية
- توليد خطابات رسمية تلقائياً
- أرقام مرجعية فريدة
- رموز QR للتحقق من صحة الخطابات
- تصدير PDF للطباعة

### 📊 التقارير والإحصائيات
- تقارير استهلاك الوقود
- تقارير نشاط المحطات
- تحليل استخدام المركبات
- تقارير الحصص والاستهلاك
- تصدير Excel و PDF

## 🏗️ البنية التقنية

### Backend
- **PHP 7.4+** مع بنية MVC مخصصة
- **SQLite3** لقاعدة البيانات
- **FPDF** لتوليد ملفات PDF
- **Router** مخصص لمعالجة الطلبات

### Frontend
- **HTML5** + **Bootstrap 5**
- **jQuery** + **AJAX** للتفاعل
- **Chart.js** للرسوم البيانية
- **Select2** للقوائم المنسدلة المتقدمة
- **Font Awesome** للأيقونات

### قاعدة البيانات
```sql
- users (المستخدمون)
- vehicles (المركبات)
- stations (المحطات)
- fuel_requests (طلبات الوقود)
- letters (الخطابات)
- activity_logs (سجل النشاطات)
```

## 🚀 التثبيت والإعداد

### المتطلبات
- خادم ويب (Apache/Nginx)
- PHP 7.4 أو أحدث
- SQLite3
- مساحة تخزين للملفات

### خطوات التثبيت

1. **نسخ الملفات**
```bash
git clone [repository-url]
cd DESL_2
```

2. **إعداد الصلاحيات**
```bash
chmod 755 -R .
chmod 777 database/
chmod 777 storage/
```

3. **الوصول للنظام**
- افتح المتصفح وانتقل إلى: `http://localhost/DESL_2`
- استخدم بيانات المدير الافتراضية:
  - البريد الإلكتروني: `<EMAIL>`
  - كلمة المرور: `admin123`

## 👥 أدوار المستخدمين

### 🔧 مدير النظام (Admin)
- إدارة جميع أجزاء النظام
- إضافة وتعديل المستخدمين
- إعداد المحطات والمركبات
- عرض جميع التقارير

### 👨‍💼 مشرف (Supervisor)
- إدارة طلبات الوقود
- الموافقة أو رفض الطلبات
- عرض تقارير القسم
- إدارة المركبات

### 🏢 جهة طلب (Requester)
- إنشاء طلبات وقود جديدة
- تتبع حالة الطلبات
- عرض تاريخ الطلبات
- طباعة الخطابات

### ⛽ محطة وقود (Station)
- عرض طلبات المحطة
- تأكيد صرف الوقود
- تتبع إحصائيات المحطة
- التحقق من الخطابات

### 📋 مراجع مركزي (Reviewer)
- مراجعة الطلبات
- إنشاء التقارير
- تدقيق العمليات
- تحليل البيانات

## 📱 تطبيق الموبايل (مستقبلي)

النظام مصمم ليدعم تطبيق موبايل مستقبلي باستخدام:
- **Flutter** أو **React Native**
- **REST API** للتكامل
- **Push Notifications** للتنبيهات
- **GPS** لتحديد أقرب المحطات

## 🔒 الأمان

- تشفير كلمات المرور باستخدام `password_hash()`
- حماية من هجمات SQL Injection
- تحقق من صحة البيانات المدخلة
- حماية من هجمات CSRF
- تسجيل جميع النشاطات

## 📈 الإحصائيات والتقارير

### تقارير متاحة:
1. **تقرير استهلاك الوقود**
   - حسب الفترة الزمنية
   - حسب نوع الوقود
   - حسب الجهة أو المحطة

2. **تقرير نشاط المحطات**
   - عدد الطلبات المعالجة
   - كمية الوقود المصروفة
   - أوقات الذروة

3. **تقرير استخدام المركبات**
   - معدل الاستهلاك
   - تكرار الطلبات
   - تحليل الحصص

4. **التقرير الشهري الموجز**
   - إحصائيات عامة
   - مؤشرات الأداء
   - اتجاهات الاستهلاك

## 🛠️ التطوير والصيانة

### إضافة مميزات جديدة
1. إنشاء Model جديد في `models/`
2. إنشاء Controller في `controllers/`
3. إنشاء View في `views/`
4. تحديث Router في `index.php`

### قاعدة البيانات
- النسخ الاحتياطي: `sqlite3 database/fuel_system.db .dump > backup.sql`
- الاستعادة: `sqlite3 database/fuel_system.db < backup.sql`

## 📞 الدعم والمساعدة

للحصول على الدعم أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- التواصل مع فريق التطوير
- مراجعة الوثائق التقنية

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

- وزارة المالية والتخطيط الاقتصادي - ولاية الخرطوم
- فريق التطوير والتصميم
- جميع المساهمين في المشروع

---

**© 2025 وزارة المالية والتخطيط الاقتصادي - ولاية الخرطوم**
