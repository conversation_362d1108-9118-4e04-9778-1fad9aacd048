/* نظام صرف حصص الوقود الذكي - ملف الأنماط الرئيسي */

/* الإعدادات العامة */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --border-radius: 10px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* الخطوط العربية */
body {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
}

/* تحسينات Bootstrap */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    border-bottom: none;
    font-weight: 600;
}

.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
    padding: 10px 20px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
    padding: 12px 15px;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* شريط التنقل */
.navbar {
    box-shadow: var(--box-shadow);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.nav-link:hover {
    transform: translateY(-1px);
}

/* البطاقات الإحصائية */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    border-radius: var(--border-radius);
    color: white;
    transition: var(--transition);
}

.stats-card:hover {
    transform: scale(1.05);
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-card h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0;
}

.stats-card .card-title {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

/* جداول البيانات */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
    transform: scale(1.01);
}

.table td {
    padding: 12px 15px;
    vertical-align: middle;
    border-color: #e9ecef;
}

/* شارات الحالة */
.badge {
    font-size: 0.8rem;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 500;
}

.badge-pending {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.badge-approved {
    background-color: var(--success-color);
    color: white;
}

.badge-rejected {
    background-color: var(--danger-color);
    color: white;
}

/* شريط التقدم */
.progress {
    height: 8px;
    border-radius: 10px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 15px 20px;
    margin-bottom: 20px;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* نماذج الطلبات */
.request-form {
    background: white;
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--box-shadow);
}

.form-section {
    margin-bottom: 30px;
    padding: 20px;
    background: var(--light-color);
    border-radius: var(--border-radius);
}

.form-section h5 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-weight: 600;
}

/* بطاقات المحطات */
.station-card {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 20px;
    transition: var(--transition);
    cursor: pointer;
    background: white;
}

.station-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.2);
    transform: translateY(-2px);
}

.station-card.selected {
    border-color: var(--success-color);
    background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
}

.station-card h6 {
    margin-bottom: 10px;
    font-weight: 600;
}

/* خيارات نوع الوقود */
.fuel-type-option {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    background: white;
}

.fuel-type-option:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.fuel-type-option.selected {
    border-color: var(--success-color);
    background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
}

.fuel-type-option i {
    color: var(--primary-color);
    margin-bottom: 10px;
}

/* معلومات المركبة */
.vehicle-info-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #dee2e6;
    border-radius: var(--border-radius);
    padding: 20px;
    transition: var(--transition);
}

.vehicle-info-card.found {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-color: var(--success-color);
}

/* شريط الحصة */
.quota-progress {
    height: 20px;
    border-radius: 10px;
    background-color: #e9ecef;
    overflow: hidden;
}

.quota-progress .progress-bar {
    transition: width 0.6s ease;
}

/* التقارير والرسوم البيانية */
.chart-container {
    position: relative;
    height: 400px;
    margin-bottom: 30px;
}

.report-filters {
    background: var(--light-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 30px;
}

/* أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-action {
    min-width: 120px;
    padding: 10px 20px;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .card-body {
        padding: 15px;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .stats-card h3 {
        font-size: 2rem;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
}

/* تحسينات الطباعة */
@media print {
    .navbar,
    .btn,
    .card-header {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    body {
        font-size: 12px;
    }
}

/* تأثيرات التحميل */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* تحسينات إضافية */
.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.shadow-sm {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.shadow {
    box-shadow: var(--box-shadow) !important;
}

.shadow-lg {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
}

/* تحسينات التركيز */
.form-control:focus,
.form-select:focus,
.btn:focus {
    outline: none;
}

/* تحسينات الألوان */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #1e7e34 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #d39e00 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #117a8b 100%);
}
