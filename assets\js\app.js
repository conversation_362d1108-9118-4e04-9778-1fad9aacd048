/**
 * نظام صرف حصص الوقود الذكي
 * ملف JavaScript الرئيسي
 */

// إعدادات عامة
const FuelSystem = {
    baseUrl: '/DESL_2',
    apiUrl: '/DESL_2/api',
    
    // تهيئة النظام
    init: function() {
        this.setupEventListeners();
        this.initializeComponents();
        this.setupAjaxDefaults();
    },
    
    // إعداد مستمعي الأحداث
    setupEventListeners: function() {
        // تأكيد الحذف
        $(document).on('click', '.btn-delete', this.confirmDelete);
        
        // تحديث الصفحة التلقائي
        if (window.location.pathname.includes('dashboard')) {
            setInterval(this.updateDashboardStats, 30000);
        }
        
        // إعداد النماذج
        $('form').on('submit', this.handleFormSubmit);
        
        // إعداد البحث المباشر
        $('.search-input').on('input', this.debounce(this.handleSearch, 300));
    },
    
    // تهيئة المكونات
    initializeComponents: function() {
        // تهيئة التلميحات
        $('[data-bs-toggle="tooltip"]').tooltip();
        
        // تهيئة النوافذ المنبثقة
        $('[data-bs-toggle="popover"]').popover();
        
        // تهيئة Select2
        if ($.fn.select2) {
            $('.select2').select2({
                theme: 'bootstrap-5',
                language: 'ar'
            });
        }
        
        // تهيئة DataTables
        if ($.fn.DataTable) {
            $('.data-table').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.0/i18n/ar.json'
                },
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']]
            });
        }
    },
    
    // إعداد AJAX الافتراضي
    setupAjaxDefaults: function() {
        $.ajaxSetup({
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            error: function(xhr, status, error) {
                FuelSystem.showAlert('حدث خطأ في الاتصال بالخادم', 'danger');
                console.error('AJAX Error:', error);
            }
        });
    },
    
    // تأكيد الحذف
    confirmDelete: function(e) {
        e.preventDefault();
        
        const element = $(this);
        const itemName = element.data('item-name') || 'هذا العنصر';
        
        if (confirm(`هل أنت متأكد من حذف ${itemName}؟\nلا يمكن التراجع عن هذا الإجراء.`)) {
            const form = element.closest('form');
            if (form.length) {
                form.submit();
            } else {
                // إرسال طلب AJAX للحذف
                const url = element.data('url') || element.attr('href');
                FuelSystem.deleteItem(url, element);
            }
        }
    },
    
    // حذف عنصر عبر AJAX
    deleteItem: function(url, element) {
        $.post(url, {_method: 'DELETE'})
            .done(function(response) {
                if (response.success) {
                    FuelSystem.showAlert(response.message || 'تم الحذف بنجاح', 'success');
                    element.closest('tr').fadeOut();
                } else {
                    FuelSystem.showAlert(response.error || 'فشل في الحذف', 'danger');
                }
            })
            .fail(function() {
                FuelSystem.showAlert('حدث خطأ أثناء الحذف', 'danger');
            });
    },
    
    // معالجة إرسال النماذج
    handleFormSubmit: function(e) {
        const form = $(this);
        const submitBtn = form.find('button[type="submit"]');
        
        // تعطيل الزر لمنع الإرسال المتكرر
        submitBtn.prop('disabled', true);
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...');
        
        // إعادة تفعيل الزر بعد 3 ثوان
        setTimeout(function() {
            submitBtn.prop('disabled', false);
            submitBtn.html(submitBtn.data('original-text') || 'إرسال');
        }, 3000);
    },
    
    // البحث المباشر
    handleSearch: function() {
        const query = $(this).val();
        const target = $(this).data('target');
        
        if (query.length >= 2) {
            FuelSystem.performSearch(query, target);
        }
    },
    
    // تنفيذ البحث
    performSearch: function(query, target) {
        $.get(`${this.apiUrl}/search`, {q: query, target: target})
            .done(function(results) {
                FuelSystem.displaySearchResults(results, target);
            });
    },
    
    // عرض نتائج البحث
    displaySearchResults: function(results, target) {
        const container = $(`#${target}-results`);
        container.empty();
        
        if (results.length === 0) {
            container.html('<div class="alert alert-info">لا توجد نتائج</div>');
            return;
        }
        
        results.forEach(function(item) {
            const resultHtml = FuelSystem.createSearchResultItem(item, target);
            container.append(resultHtml);
        });
    },
    
    // إنشاء عنصر نتيجة بحث
    createSearchResultItem: function(item, target) {
        switch (target) {
            case 'vehicles':
                return `
                    <div class="search-result-item p-2 border-bottom" data-id="${item.id}">
                        <strong>${item.plate_number}</strong> - ${item.owner}
                        <small class="text-muted d-block">${item.vehicle_type}</small>
                    </div>
                `;
            case 'stations':
                return `
                    <div class="search-result-item p-2 border-bottom" data-id="${item.id}">
                        <strong>${item.name}</strong>
                        <small class="text-muted d-block">${item.location}</small>
                    </div>
                `;
            default:
                return `<div class="search-result-item p-2 border-bottom">${item.name}</div>`;
        }
    },
    
    // تحديث إحصائيات لوحة التحكم
    updateDashboardStats: function() {
        $.get(`${FuelSystem.baseUrl}/dashboard/quick-stats`)
            .done(function(data) {
                $('#todayRequests').text(data.total_requests_today || 0);
                $('#pendingRequests').text(data.pending_requests || 0);
                $('#todayFuel').text((data.total_fuel_today || 0).toLocaleString());
                $('#activeStations').text(data.active_stations || 0);
            });
    },
    
    // عرض تنبيه
    showAlert: function(message, type = 'info', duration = 5000) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show position-fixed" 
                 style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                <i class="fas fa-${this.getAlertIcon(type)} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        $('body').append(alertHtml);
        
        // إزالة التنبيه تلقائياً
        setTimeout(function() {
            $('.alert').last().fadeOut();
        }, duration);
    },
    
    // الحصول على أيقونة التنبيه
    getAlertIcon: function(type) {
        const icons = {
            success: 'check-circle',
            danger: 'exclamation-triangle',
            warning: 'exclamation-circle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    },
    
    // تأخير تنفيذ الدالة
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // تحميل البيانات عبر AJAX
    loadData: function(url, callback) {
        $.get(url)
            .done(callback)
            .fail(function() {
                FuelSystem.showAlert('فشل في تحميل البيانات', 'danger');
            });
    },
    
    // إرسال البيانات عبر AJAX
    sendData: function(url, data, callback) {
        $.post(url, data)
            .done(callback)
            .fail(function(xhr) {
                const response = xhr.responseJSON;
                const message = response?.error || 'حدث خطأ أثناء المعالجة';
                FuelSystem.showAlert(message, 'danger');
            });
    },
    
    // تنسيق الأرقام
    formatNumber: function(number) {
        return new Intl.NumberFormat('ar-SA').format(number);
    },
    
    // تنسيق التاريخ
    formatDate: function(date) {
        return new Intl.DateTimeFormat('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }).format(new Date(date));
    },
    
    // التحقق من صحة النموذج
    validateForm: function(form) {
        let isValid = true;
        const errors = [];
        
        form.find('[required]').each(function() {
            const field = $(this);
            if (!field.val().trim()) {
                isValid = false;
                field.addClass('is-invalid');
                errors.push(`${field.data('label') || field.attr('name')} مطلوب`);
            } else {
                field.removeClass('is-invalid');
            }
        });
        
        if (!isValid) {
            FuelSystem.showAlert(errors.join('<br>'), 'danger');
        }
        
        return isValid;
    }
};

// مساعدات إضافية
const Utils = {
    // نسخ النص إلى الحافظة
    copyToClipboard: function(text) {
        navigator.clipboard.writeText(text).then(function() {
            FuelSystem.showAlert('تم نسخ النص', 'success', 2000);
        });
    },
    
    // تحميل ملف
    downloadFile: function(url, filename) {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    },
    
    // طباعة العنصر
    printElement: function(elementId) {
        const element = document.getElementById(elementId);
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>طباعة</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                    <style>
                        body { font-family: Arial, sans-serif; }
                        @media print { .no-print { display: none !important; } }
                    </style>
                </head>
                <body onload="window.print(); window.close();">
                    ${element.innerHTML}
                </body>
            </html>
        `);
        printWindow.document.close();
    }
};

// تهيئة النظام عند تحميل الصفحة
$(document).ready(function() {
    FuelSystem.init();
});

// تصدير للاستخدام العام
window.FuelSystem = FuelSystem;
window.Utils = Utils;
