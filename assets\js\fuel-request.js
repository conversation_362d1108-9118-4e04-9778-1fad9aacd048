/**
 * نظام صرف حصص الوقود الذكي
 * JavaScript لصفحة طلب الوقود
 */

$(document).ready(function() {
    const FuelRequest = {
        selectedVehicle: null,
        selectedStation: null,
        selectedFuelType: null,
        
        init: function() {
            this.setupEventListeners();
            this.initializeForm();
        },
        
        setupEventListeners: function() {
            // البحث عن المركبة
            $('#searchVehicle').on('click', this.searchVehicle.bind(this));
            $('#plate_number').on('keypress', function(e) {
                if (e.which === 13) {
                    e.preventDefault();
                    FuelRequest.searchVehicle();
                }
            });
            
            // اختيار نوع الوقود
            $('.fuel-type-option').on('click', this.selectFuelType.bind(this));
            
            // اختيار المحطة
            $('.station-card').on('click', this.selectStation.bind(this));
            
            // التحقق من الكمية
            $('#quantity').on('input', this.validateQuantity.bind(this));
            
            // إرسال النموذج
            $('#fuelRequestForm').on('submit', this.submitForm.bind(this));
        },
        
        initializeForm: function() {
            this.updateSubmitButton();
        },
        
        searchVehicle: function() {
            const plateNumber = $('#plate_number').val().trim();
            
            if (!plateNumber) {
                this.showAlert('يرجى إدخال رقم اللوحة', 'warning');
                return;
            }
            
            // إظهار مؤشر التحميل
            const searchBtn = $('#searchVehicle');
            const originalText = searchBtn.html();
            searchBtn.html('<i class="fas fa-spinner fa-spin"></i>').prop('disabled', true);
            
            // إخفاء معلومات المركبة السابقة
            $('#vehicleInfo').hide().removeClass('found');
            
            // البحث عن المركبة
            $.get('/DESL_2/api/vehicles/search', { plate_number: plateNumber })
                .done(this.handleVehicleFound.bind(this))
                .fail(this.handleVehicleNotFound.bind(this))
                .always(function() {
                    searchBtn.html(originalText).prop('disabled', false);
                });
        },
        
        handleVehicleFound: function(vehicle) {
            this.selectedVehicle = vehicle;
            
            // عرض معلومات المركبة
            $('#vehicleOwner').text(vehicle.owner);
            $('#vehicleType').text(vehicle.vehicle_type);
            $('#vehicleChasis').text(vehicle.chasis_number);
            $('#vehicleTank').text(vehicle.tank_capacity + ' لتر');
            
            // عرض حالة الحصة
            if (vehicle.quota_status) {
                const quota = vehicle.quota_status;
                const percentage = (quota.consumed / quota.monthly_limit) * 100;
                
                $('#quotaUsed').text(quota.consumed);
                $('#quotaRemaining').text(quota.remaining);
                $('#quotaProgress').css('width', percentage + '%');
                
                // تغيير لون شريط التقدم حسب النسبة
                const progressBar = $('#quotaProgress');
                progressBar.removeClass('bg-success bg-warning bg-danger');
                
                if (percentage < 50) {
                    progressBar.addClass('bg-success');
                } else if (percentage < 80) {
                    progressBar.addClass('bg-warning');
                } else {
                    progressBar.addClass('bg-danger');
                }
            }
            
            // إظهار بطاقة المركبة
            $('#vehicleInfo').addClass('found').fadeIn();
            
            // تحديد نوع الوقود تلقائياً
            this.autoSelectFuelType(vehicle.fuel_type);
            
            this.updateSubmitButton();
            this.showAlert('تم العثور على المركبة بنجاح', 'success');
        },
        
        handleVehicleNotFound: function() {
            this.selectedVehicle = null;
            $('#vehicleInfo').hide().removeClass('found');
            this.updateSubmitButton();
            this.showAlert('لم يتم العثور على المركبة. تأكد من رقم اللوحة.', 'danger');
        },
        
        autoSelectFuelType: function(fuelType) {
            $('.fuel-type-option').removeClass('selected');
            const fuelOption = $(`.fuel-type-option[data-fuel-type="${fuelType}"]`);
            
            if (fuelOption.length) {
                fuelOption.addClass('selected');
                this.selectedFuelType = fuelType;
                $('#fuel_type').val(fuelType);
                this.updateSubmitButton();
            }
        },
        
        selectFuelType: function(e) {
            const option = $(e.currentTarget);
            const fuelType = option.data('fuel-type');
            
            // التحقق من توافق نوع الوقود مع المركبة
            if (this.selectedVehicle && this.selectedVehicle.fuel_type !== fuelType) {
                this.showAlert('نوع الوقود المختار لا يتطابق مع نوع وقود المركبة', 'warning');
                return;
            }
            
            $('.fuel-type-option').removeClass('selected');
            option.addClass('selected');
            
            this.selectedFuelType = fuelType;
            $('#fuel_type').val(fuelType);
            
            this.updateSubmitButton();
        },
        
        selectStation: function(e) {
            const card = $(e.currentTarget);
            const stationId = card.data('station-id');
            
            $('.station-card').removeClass('selected');
            card.addClass('selected');
            
            this.selectedStation = stationId;
            $('#station_id').val(stationId);
            
            this.updateSubmitButton();
        },
        
        validateQuantity: function() {
            const quantity = parseInt($('#quantity').val());
            const quantityField = $('#quantity');
            
            // إزالة الرسائل السابقة
            quantityField.removeClass('is-invalid is-valid');
            $('.quantity-feedback').remove();
            
            if (!quantity || quantity <= 0) {
                return;
            }
            
            // التحقق من الحد الأقصى
            if (quantity > 1000) {
                quantityField.addClass('is-invalid');
                quantityField.after('<div class="invalid-feedback quantity-feedback">الحد الأقصى 1000 لتر</div>');
                return;
            }
            
            // التحقق من الحصة المتاحة
            if (this.selectedVehicle && this.selectedVehicle.quota_status) {
                const remaining = this.selectedVehicle.quota_status.remaining;
                
                if (quantity > remaining) {
                    quantityField.addClass('is-invalid');
                    quantityField.after(`<div class="invalid-feedback quantity-feedback">الكمية تتجاوز الحصة المتاحة (${remaining} لتر)</div>`);
                    return;
                }
            }
            
            quantityField.addClass('is-valid');
            this.updateSubmitButton();
        },
        
        updateSubmitButton: function() {
            const submitBtn = $('#submitBtn');
            const isValid = this.selectedVehicle && 
                           this.selectedStation && 
                           this.selectedFuelType && 
                           $('#quantity').val() > 0 &&
                           !$('#quantity').hasClass('is-invalid');
            
            submitBtn.prop('disabled', !isValid);
            
            if (isValid) {
                submitBtn.removeClass('btn-secondary').addClass('btn-primary');
            } else {
                submitBtn.removeClass('btn-primary').addClass('btn-secondary');
            }
        },
        
        submitForm: function(e) {
            e.preventDefault();
            
            // التحقق النهائي
            if (!this.validateFinalForm()) {
                return;
            }
            
            // إظهار مؤشر التحميل
            const submitBtn = $('#submitBtn');
            const originalText = submitBtn.html();
            submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...').prop('disabled', true);
            
            // إرسال النموذج
            const formData = $('#fuelRequestForm').serialize();
            
            $.post('/DESL_2/fuel/request', formData)
                .done(this.handleSubmitSuccess.bind(this))
                .fail(this.handleSubmitError.bind(this))
                .always(function() {
                    submitBtn.html(originalText).prop('disabled', false);
                });
        },
        
        validateFinalForm: function() {
            const errors = [];
            
            if (!this.selectedVehicle) {
                errors.push('يجب البحث عن المركبة أولاً');
            }
            
            if (!this.selectedFuelType) {
                errors.push('يجب اختيار نوع الوقود');
            }
            
            if (!this.selectedStation) {
                errors.push('يجب اختيار محطة الوقود');
            }
            
            const quantity = parseInt($('#quantity').val());
            if (!quantity || quantity <= 0) {
                errors.push('يجب إدخال كمية صحيحة');
            }
            
            if (errors.length > 0) {
                this.showAlert(errors.join('<br>'), 'danger');
                return false;
            }
            
            return true;
        },
        
        handleSubmitSuccess: function(response) {
            this.showAlert('تم إرسال الطلب بنجاح!', 'success');
            
            // إعادة تعيين النموذج
            setTimeout(function() {
                window.location.href = '/DESL_2/dashboard';
            }, 2000);
        },
        
        handleSubmitError: function(xhr) {
            const response = xhr.responseJSON;
            const message = response?.error || 'حدث خطأ أثناء إرسال الطلب';
            this.showAlert(message, 'danger');
        },
        
        showAlert: function(message, type) {
            // إزالة التنبيهات السابقة
            $('.temp-alert').remove();
            
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show temp-alert">
                    <i class="fas fa-${this.getAlertIcon(type)} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            $('.container').prepend(alertHtml);
            
            // التمرير إلى أعلى الصفحة
            $('html, body').animate({ scrollTop: 0 }, 500);
        },
        
        getAlertIcon: function(type) {
            const icons = {
                success: 'check-circle',
                danger: 'exclamation-triangle',
                warning: 'exclamation-circle',
                info: 'info-circle'
            };
            return icons[type] || 'info-circle';
        }
    };
    
    // تهيئة صفحة طلب الوقود
    FuelRequest.init();
    
    // إضافة تأثيرات بصرية
    $('.fuel-type-option, .station-card').hover(
        function() {
            $(this).addClass('shadow-sm');
        },
        function() {
            $(this).removeClass('shadow-sm');
        }
    );
    
    // التحقق من الكمية عند الكتابة
    $('#quantity').on('input', function() {
        const value = $(this).val();
        if (value && value > 0) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });
    
    // تحسين تجربة المستخدم
    $('#plate_number').on('input', function() {
        const value = $(this).val().toUpperCase();
        $(this).val(value);
        
        // إخفاء معلومات المركبة عند تغيير رقم اللوحة
        if (FuelRequest.selectedVehicle) {
            $('#vehicleInfo').fadeOut();
            FuelRequest.selectedVehicle = null;
            FuelRequest.updateSubmitButton();
        }
    });
});
