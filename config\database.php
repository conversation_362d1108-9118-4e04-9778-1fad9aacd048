<?php
// إعدادات قاعدة البيانات

class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $this->connection = new PDO('sqlite:' . ROOT_PATH . '/database/fuel_system.db');
            $this->connection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->connection->exec("PRAGMA foreign_keys = ON");
            $this->createTables();
        } catch (PDOException $e) {
            die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    private function createTables() {
        $tables = [
            // جدول المستخدمين
            "CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                role VARCHAR(20) NOT NULL,
                station_id INTEGER,
                is_active BOOLEAN DEFAULT 1,
                last_login DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (station_id) REFERENCES stations(id)
            )",
            
            // جدول المحطات
            "CREATE TABLE IF NOT EXISTS stations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                code VARCHAR(20) UNIQUE NOT NULL,
                location VARCHAR(200) NOT NULL,
                latitude DECIMAL(10,8),
                longitude DECIMAL(11,8),
                managed_by VARCHAR(50) NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )",
            
            // جدول المركبات
            "CREATE TABLE IF NOT EXISTS vehicles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                plate_number VARCHAR(20) UNIQUE NOT NULL,
                chasis_number VARCHAR(50) UNIQUE NOT NULL,
                owner VARCHAR(100) NOT NULL,
                vehicle_type VARCHAR(50) NOT NULL,
                fuel_type VARCHAR(20) NOT NULL,
                tank_capacity INTEGER NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )",
            
            // جدول طلبات الوقود
            "CREATE TABLE IF NOT EXISTS fuel_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                vehicle_id INTEGER NOT NULL,
                station_id INTEGER NOT NULL,
                user_id INTEGER NOT NULL,
                fuel_type VARCHAR(20) NOT NULL,
                quantity INTEGER NOT NULL,
                status VARCHAR(20) DEFAULT 'pending',
                request_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                approved_date DATETIME,
                notes TEXT,
                FOREIGN KEY (vehicle_id) REFERENCES vehicles(id),
                FOREIGN KEY (station_id) REFERENCES stations(id),
                FOREIGN KEY (user_id) REFERENCES users(id)
            )",
            
            // جدول الخطابات
            "CREATE TABLE IF NOT EXISTS letters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                request_id INTEGER NOT NULL,
                letter_number VARCHAR(50) UNIQUE NOT NULL,
                generated_pdf VARCHAR(255),
                qr_hash VARCHAR(255) NOT NULL,
                printed_at DATETIME,
                is_used BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (request_id) REFERENCES fuel_requests(id)
            )",
            
            // جدول سجل النشاطات
            "CREATE TABLE IF NOT EXISTS activity_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                action VARCHAR(100) NOT NULL,
                table_name VARCHAR(50),
                record_id INTEGER,
                old_values TEXT,
                new_values TEXT,
                ip_address VARCHAR(45),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )"
        ];
        
        foreach ($tables as $table) {
            $this->connection->exec($table);
        }
        
        $this->insertDefaultData();
        $this->createSovereignTables();
    }

    private function createSovereignTables() {
        // جدول التوقيعات الرقمية السيادية
        $this->connection->exec("
            CREATE TABLE IF NOT EXISTS sovereign_signatures (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                document_id INTEGER NOT NULL,
                signer_id INTEGER NOT NULL,
                signer_name TEXT NOT NULL,
                signer_title TEXT NOT NULL,
                signature_level TEXT NOT NULL,
                signature_hash TEXT UNIQUE NOT NULL,
                certificate_serial TEXT NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                ip_address TEXT,
                user_agent TEXT,
                validity_start DATETIME NOT NULL,
                validity_end DATETIME NOT NULL,
                status TEXT DEFAULT 'active',
                FOREIGN KEY (signer_id) REFERENCES users(id)
            )
        ");

        // جدول الأختام السيادية
        $this->connection->exec("
            CREATE TABLE IF NOT EXISTS sovereign_seals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                document_id INTEGER NOT NULL,
                seal_type TEXT NOT NULL,
                seal_authority TEXT NOT NULL,
                seal_hash TEXT UNIQUE NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                legal_authority TEXT NOT NULL,
                status TEXT DEFAULT 'active'
            )
        ");

        // جدول رموز QR السيادية
        $this->connection->exec("
            CREATE TABLE IF NOT EXISTS sovereign_qr_codes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                document_id INTEGER NOT NULL,
                document_type TEXT NOT NULL,
                qr_hash TEXT UNIQUE NOT NULL,
                encrypted_data TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                expires_at DATETIME NOT NULL,
                status TEXT DEFAULT 'active'
            )
        ");

        // جدول سجل التدقيق السيادي
        $this->connection->exec("
            CREATE TABLE IF NOT EXISTS sovereign_audit_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                action TEXT NOT NULL,
                details TEXT,
                user_id INTEGER,
                ip_address TEXT,
                user_agent TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ");

        // جدول حصص الوزارات
        $this->connection->exec("
            CREATE TABLE IF NOT EXISTS ministry_quotas (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ministry_code TEXT NOT NULL,
                ministry_name TEXT NOT NULL,
                year INTEGER NOT NULL,
                month INTEGER NOT NULL,
                allocated_amount REAL NOT NULL,
                consumed_amount REAL DEFAULT 0,
                remaining_amount REAL NOT NULL,
                status TEXT DEFAULT 'active',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(ministry_code, year, month)
            )
        ");

        // جدول سجل استهلاك الحصص
        $this->connection->exec("
            CREATE TABLE IF NOT EXISTS quota_consumption_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ministry_code TEXT NOT NULL,
                amount REAL NOT NULL,
                year INTEGER NOT NULL,
                month INTEGER NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                user_id INTEGER,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ");

        // جدول سجل تدقيق الحصص
        $this->connection->exec("
            CREATE TABLE IF NOT EXISTS quota_audit_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                action TEXT NOT NULL,
                details TEXT,
                user_id INTEGER,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ");

        // تحديث جدول المركبات لإضافة حقول سيادية
        $this->connection->exec("
            ALTER TABLE vehicles ADD COLUMN ministry_code TEXT DEFAULT NULL
        ");

        $this->connection->exec("
            ALTER TABLE vehicles ADD COLUMN vehicle_category TEXT DEFAULT 'service'
        ");

        $this->connection->exec("
            ALTER TABLE vehicles ADD COLUMN monthly_quota REAL DEFAULT 500
        ");

        // تحديث جدول طلبات الوقود لإضافة حقول سيادية
        $this->connection->exec("
            ALTER TABLE fuel_requests ADD COLUMN ministry_code TEXT DEFAULT NULL
        ");

        $this->connection->exec("
            ALTER TABLE fuel_requests ADD COLUMN priority_level INTEGER DEFAULT 3
        ");

        $this->connection->exec("
            ALTER TABLE fuel_requests ADD COLUMN approval_level TEXT DEFAULT 'supervisor'
        ");

        $this->connection->exec("
            ALTER TABLE fuel_requests ADD COLUMN emergency_justification TEXT DEFAULT NULL
        ");
    }

    private function insertDefaultData() {
        // إدراج مستخدم افتراضي
        $stmt = $this->connection->prepare("SELECT COUNT(*) FROM users");
        $stmt->execute();
        if ($stmt->fetchColumn() == 0) {
            $defaultPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $this->connection->exec("
                INSERT INTO users (name, email, password, role) 
                VALUES ('مدير النظام', '<EMAIL>', '$defaultPassword', 'admin')
            ");
        }
        
        // إدراج محطات افتراضية
        $stmt = $this->connection->prepare("SELECT COUNT(*) FROM stations");
        $stmt->execute();
        if ($stmt->fetchColumn() == 0) {
            $stations = [
                ['محطة الخرطوم المركزية', 'KH001', 'شارع النيل - الخرطوم', 'حكومية'],
                ['محطة بحري الشمالية', 'BH001', 'حي الصابرات - بحري', 'حكومية'],
                ['محطة أمدرمان الغربية', 'OM001', 'السوق الشعبي - أمدرمان', 'حكومية'],
                ['محطة الكلاكلة', 'KL001', 'منطقة الكلاكلة', 'حكومية'],
                ['محطة شرق النيل', 'SN001', 'شرق النيل', 'حكومية']
            ];
            
            foreach ($stations as $station) {
                $this->connection->exec("
                    INSERT INTO stations (name, code, location, managed_by) 
                    VALUES ('{$station[0]}', '{$station[1]}', '{$station[2]}', '{$station[3]}')
                ");
            }
        }
    }
}

// إنشاء اتصال قاعدة البيانات
if (!file_exists(ROOT_PATH . '/database')) {
    mkdir(ROOT_PATH . '/database', 0755, true);
}

$db = Database::getInstance();
?>
