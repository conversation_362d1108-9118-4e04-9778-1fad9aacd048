<?php

class AuthController {
    private $userModel;
    
    public function __construct() {
        $this->userModel = new User();
    }
    
    public function showLogin() {
        // إذا كان المستخدم مسجل دخول بالفعل، إعادة توجيه للوحة التحكم
        if (isset($_SESSION['user_id'])) {
            header('Location: /DESL_2/dashboard');
            exit;
        }
        
        $this->render('auth/login');
    }
    
    public function login() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /DESL_2/login');
            exit;
        }
        
        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';
        $errors = [];
        
        // التحقق من صحة البيانات
        if (empty($email)) {
            $errors[] = 'البريد الإلكتروني مطلوب';
        }
        
        if (empty($password)) {
            $errors[] = 'كلمة المرور مطلوبة';
        }
        
        if (!empty($errors)) {
            $this->render('auth/login', ['errors' => $errors]);
            return;
        }
        
        // التحقق من محاولات تسجيل الدخول
        if ($this->isLoginBlocked($email)) {
            $errors[] = 'تم حظر تسجيل الدخول مؤقتاً بسبب المحاولات المتكررة';
            $this->render('auth/login', ['errors' => $errors]);
            return;
        }
        
        // محاولة تسجيل الدخول
        $user = $this->userModel->authenticate($email, $password);
        
        if ($user) {
            // تسجيل دخول ناجح
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['name'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['station_id'] = $user['station_id'];
            $_SESSION['login_time'] = time();
            
            // مسح محاولات تسجيل الدخول الفاشلة
            $this->clearLoginAttempts($email);
            
            // تسجيل النشاط
            $this->userModel->logActivity($user['id'], 'تسجيل دخول');
            
            // إعادة التوجيه حسب دور المستخدم
            $redirectUrl = $this->getRedirectUrl($user['role']);
            header("Location: $redirectUrl");
            exit;
        } else {
            // تسجيل دخول فاشل
            $this->recordLoginAttempt($email);
            $errors[] = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
            $this->render('auth/login', ['errors' => $errors]);
        }
    }
    
    public function logout() {
        if (isset($_SESSION['user_id'])) {
            // تسجيل النشاط
            $this->userModel->logActivity($_SESSION['user_id'], 'تسجيل خروج');
        }
        
        // مسح جلسة المستخدم
        session_destroy();
        
        header('Location: /DESL_2/login');
        exit;
    }
    
    public function checkAuth() {
        if (!isset($_SESSION['user_id'])) {
            header('Location: /DESL_2/login');
            exit;
        }
        
        // التحقق من انتهاء صلاحية الجلسة
        $config = require CONFIG_PATH . '/app.php';
        $sessionTimeout = $config['session_timeout'];
        
        if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time']) > $sessionTimeout) {
            session_destroy();
            header('Location: /DESL_2/login?expired=1');
            exit;
        }
        
        // تحديث وقت آخر نشاط
        $_SESSION['last_activity'] = time();
        
        return true;
    }
    
    public function hasPermission($permission) {
        if (!isset($_SESSION['user_id'])) {
            return false;
        }
        
        return $this->userModel->hasPermission($_SESSION['user_id'], $permission);
    }
    
    private function isLoginBlocked($email) {
        $config = require CONFIG_PATH . '/app.php';
        $maxAttempts = $config['max_login_attempts'];
        
        $attempts = $_SESSION['login_attempts'][$email] ?? 0;
        $lastAttempt = $_SESSION['last_attempt_time'][$email] ?? 0;
        
        // إذا مرت 15 دقيقة، مسح المحاولات
        if (time() - $lastAttempt > 900) {
            unset($_SESSION['login_attempts'][$email]);
            unset($_SESSION['last_attempt_time'][$email]);
            return false;
        }
        
        return $attempts >= $maxAttempts;
    }
    
    private function recordLoginAttempt($email) {
        if (!isset($_SESSION['login_attempts'])) {
            $_SESSION['login_attempts'] = [];
        }
        
        if (!isset($_SESSION['last_attempt_time'])) {
            $_SESSION['last_attempt_time'] = [];
        }
        
        $_SESSION['login_attempts'][$email] = ($_SESSION['login_attempts'][$email] ?? 0) + 1;
        $_SESSION['last_attempt_time'][$email] = time();
    }
    
    private function clearLoginAttempts($email) {
        unset($_SESSION['login_attempts'][$email]);
        unset($_SESSION['last_attempt_time'][$email]);
    }
    
    private function getRedirectUrl($role) {
        $redirectUrls = [
            'admin' => '/DESL_2/dashboard',
            'supervisor' => '/DESL_2/dashboard',
            'requester' => '/DESL_2/fuel/request',
            'station' => '/DESL_2/stations',
            'reviewer' => '/DESL_2/reports'
        ];
        
        return $redirectUrls[$role] ?? '/DESL_2/dashboard';
    }
    
    private function render($view, $data = []) {
        extract($data);
        
        $viewPath = VIEWS_PATH . '/' . $view . '.php';
        
        if (file_exists($viewPath)) {
            include $viewPath;
        } else {
            echo "العرض غير موجود: $view";
        }
    }
    
    public function getCurrentUser() {
        if (!isset($_SESSION['user_id'])) {
            return null;
        }
        
        return [
            'id' => $_SESSION['user_id'],
            'name' => $_SESSION['user_name'],
            'role' => $_SESSION['user_role'],
            'email' => $_SESSION['user_email'],
            'station_id' => $_SESSION['station_id']
        ];
    }
    
    public function requireRole($requiredRole) {
        $this->checkAuth();
        
        if ($_SESSION['user_role'] !== $requiredRole && $_SESSION['user_role'] !== 'admin') {
            http_response_code(403);
            echo "غير مصرح لك بالوصول لهذه الصفحة";
            exit;
        }
    }
    
    public function requirePermission($permission) {
        $this->checkAuth();
        
        if (!$this->hasPermission($permission)) {
            http_response_code(403);
            echo "غير مصرح لك بتنفيذ هذا الإجراء";
            exit;
        }
    }
}
?>
