<?php

class DashboardController {
    private $authController;
    private $fuelRequestModel;
    private $vehicleModel;
    private $stationModel;
    private $letterModel;
    
    public function __construct() {
        $this->authController = new AuthController();
        $this->fuelRequestModel = new FuelRequest();
        $this->vehicleModel = new Vehicle();
        $this->stationModel = new Station();
        $this->letterModel = new Letter();
    }
    
    public function index() {
        $this->authController->checkAuth();
        
        $user = $this->authController->getCurrentUser();
        $data = [
            'user' => $user,
            'page_title' => 'لوحة التحكم الرئيسية'
        ];
        
        // جمع الإحصائيات حسب دور المستخدم
        switch ($user['role']) {
            case 'admin':
            case 'supervisor':
                $data = array_merge($data, $this->getAdminDashboardData());
                break;
                
            case 'requester':
                $data = array_merge($data, $this->getRequesterDashboardData($user['id']));
                break;
                
            case 'station':
                $data = array_merge($data, $this->getStationDashboardData($user['station_id']));
                break;
                
            case 'reviewer':
                $data = array_merge($data, $this->getReviewerDashboardData());
                break;
        }
        
        $this->render('dashboard/index', $data);
    }
    
    private function getAdminDashboardData() {
        // إحصائيات عامة للنظام
        $fuelStats = $this->fuelRequestModel->getStatistics('monthly');
        $vehicleStats = $this->vehicleModel->getStatistics();
        $letterStats = $this->letterModel->getStatistics('monthly');
        
        // الطلبات المعلقة
        $pendingRequests = $this->fuelRequestModel->getPendingRequests(10);
        
        // الطلبات الحديثة
        $recentRequests = $this->fuelRequestModel->getAll(['limit' => 10]);
        
        // اتجاهات شهرية
        $monthlyTrends = $this->fuelRequestModel->getMonthlyTrends();
        
        // أكثر المحطات نشاطاً
        $activeStations = $this->getActiveStations();
        
        return [
            'fuel_stats' => $fuelStats,
            'vehicle_stats' => $vehicleStats,
            'letter_stats' => $letterStats,
            'pending_requests' => $pendingRequests,
            'recent_requests' => $recentRequests,
            'monthly_trends' => $monthlyTrends,
            'active_stations' => $activeStations,
            'dashboard_type' => 'admin'
        ];
    }
    
    private function getRequesterDashboardData($userId) {
        // طلبات المستخدم
        $userRequests = $this->fuelRequestModel->getRequestsByUser($userId, 15);
        
        // إحصائيات المستخدم
        $userStats = $this->getUserStatistics($userId);
        
        // المركبات المرتبطة بالمستخدم (حسب الجهة)
        $userVehicles = $this->getUserVehicles($userId);
        
        return [
            'user_requests' => $userRequests,
            'user_stats' => $userStats,
            'user_vehicles' => $userVehicles,
            'dashboard_type' => 'requester'
        ];
    }
    
    private function getStationDashboardData($stationId) {
        if (!$stationId) {
            return ['dashboard_type' => 'station', 'error' => 'لم يتم ربط المستخدم بمحطة'];
        }
        
        // طلبات المحطة
        $stationRequests = $this->stationModel->getStationRequests($stationId, null, 20);
        
        // إحصائيات المحطة
        $stationStats = $this->stationModel->getStationStatistics($stationId, 'monthly');
        
        // الطلبات المعلقة للمحطة
        $pendingRequests = $this->stationModel->getStationRequests($stationId, 'pending', 10);
        
        // توزيع الوقود الشهري
        $fuelDistribution = $this->stationModel->getMonthlyFuelDistribution($stationId);
        
        // أكثر الجهات طلباً
        $topRequesters = $this->stationModel->getTopRequestingEntities($stationId);
        
        return [
            'station_requests' => $stationRequests,
            'station_stats' => $stationStats,
            'pending_requests' => $pendingRequests,
            'fuel_distribution' => $fuelDistribution,
            'top_requesters' => $topRequesters,
            'dashboard_type' => 'station'
        ];
    }
    
    private function getReviewerDashboardData() {
        // الطلبات التي تحتاج مراجعة
        $pendingReview = $this->fuelRequestModel->getPendingRequests(20);
        
        // إحصائيات المراجعة
        $reviewStats = $this->getReviewStatistics();
        
        // التقارير الحديثة
        $recentReports = $this->getRecentReports();
        
        return [
            'pending_review' => $pendingReview,
            'review_stats' => $reviewStats,
            'recent_reports' => $recentReports,
            'dashboard_type' => 'reviewer'
        ];
    }
    
    private function getActiveStations() {
        $stations = $this->stationModel->getAll();
        $activeStations = [];
        
        foreach ($stations as $station) {
            $stats = $this->stationModel->getStationStatistics($station['id'], 'monthly');
            $station['monthly_requests'] = $stats['total_requests'];
            $station['monthly_fuel'] = $stats['total_fuel_dispensed'];
            $activeStations[] = $station;
        }
        
        // ترتيب حسب النشاط
        usort($activeStations, function($a, $b) {
            return $b['monthly_requests'] - $a['monthly_requests'];
        });
        
        return array_slice($activeStations, 0, 5);
    }
    
    private function getUserStatistics($userId) {
        $db = Database::getInstance()->getConnection();
        
        $stmt = $db->prepare("
            SELECT 
                COUNT(*) as total_requests,
                COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_requests,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_requests,
                SUM(CASE WHEN status = 'approved' THEN quantity ELSE 0 END) as total_fuel_received,
                AVG(CASE WHEN status = 'approved' THEN quantity END) as avg_quantity
            FROM fuel_requests 
            WHERE user_id = ? 
            AND request_date >= DATE('now', 'start of month')
        ");
        
        $stmt->execute([$userId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function getUserVehicles($userId) {
        // الحصول على الجهة من بيانات المستخدم
        $user = $this->authController->getCurrentUser();
        $userModel = new User();
        $userData = $userModel->findById($user['id']);
        
        // البحث عن المركبات حسب الجهة (يمكن تحسين هذا لاحقاً)
        return $this->vehicleModel->getAll(10);
    }
    
    private function getReviewStatistics() {
        $db = Database::getInstance()->getConnection();
        
        $stmt = $db->prepare("
            SELECT 
                COUNT(*) as total_pending,
                COUNT(CASE WHEN request_date < DATE('now', '-1 day') THEN 1 END) as overdue_requests,
                COUNT(CASE WHEN request_date >= DATE('now') THEN 1 END) as today_requests
            FROM fuel_requests 
            WHERE status = 'pending'
        ");
        
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function getRecentReports() {
        // هذه دالة مؤقتة - سيتم تطويرها مع نظام التقارير
        return [];
    }
    
    public function getQuickStats() {
        $this->authController->checkAuth();
        
        header('Content-Type: application/json');
        
        $stats = [
            'total_requests_today' => $this->getTodayRequestsCount(),
            'pending_requests' => $this->getPendingRequestsCount(),
            'total_fuel_today' => $this->getTodayFuelTotal(),
            'active_stations' => $this->getActiveStationsCount()
        ];
        
        echo json_encode($stats);
    }
    
    private function getTodayRequestsCount() {
        $db = Database::getInstance()->getConnection();
        $stmt = $db->prepare("SELECT COUNT(*) FROM fuel_requests WHERE DATE(request_date) = DATE('now')");
        $stmt->execute();
        return $stmt->fetchColumn();
    }
    
    private function getPendingRequestsCount() {
        $db = Database::getInstance()->getConnection();
        $stmt = $db->prepare("SELECT COUNT(*) FROM fuel_requests WHERE status = 'pending'");
        $stmt->execute();
        return $stmt->fetchColumn();
    }
    
    private function getTodayFuelTotal() {
        $db = Database::getInstance()->getConnection();
        $stmt = $db->prepare("
            SELECT SUM(quantity) 
            FROM fuel_requests 
            WHERE DATE(request_date) = DATE('now') AND status = 'approved'
        ");
        $stmt->execute();
        return $stmt->fetchColumn() ?: 0;
    }
    
    private function getActiveStationsCount() {
        $db = Database::getInstance()->getConnection();
        $stmt = $db->prepare("SELECT COUNT(*) FROM stations WHERE is_active = 1");
        $stmt->execute();
        return $stmt->fetchColumn();
    }
    
    private function render($view, $data = []) {
        extract($data);
        
        $viewPath = VIEWS_PATH . '/' . $view . '.php';
        
        if (file_exists($viewPath)) {
            include $viewPath;
        } else {
            echo "العرض غير موجود: $view";
        }
    }
}
?>
