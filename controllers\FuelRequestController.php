<?php

class FuelRequestController {
    private $authController;
    private $fuelRequestModel;
    private $vehicleModel;
    private $stationModel;
    private $letterModel;
    
    public function __construct() {
        $this->authController = new AuthController();
        $this->fuelRequestModel = new FuelRequest();
        $this->vehicleModel = new Vehicle();
        $this->stationModel = new Station();
        $this->letterModel = new Letter();
    }
    
    public function showForm() {
        $this->authController->checkAuth();
        $this->authController->requirePermission('create_request');
        
        $user = $this->authController->getCurrentUser();
        
        // الحصول على المحطات المتاحة
        $stations = $this->stationModel->getForSelect();
        
        // الحصول على أنواع الوقود
        $config = require CONFIG_PATH . '/app.php';
        $fuelTypes = $config['fuel_types'];
        
        $data = [
            'user' => $user,
            'stations' => $stations,
            'fuel_types' => $fuelTypes,
            'page_title' => 'طلب صرف حصة وقود'
        ];
        
        $this->render('fuel/request', $data);
    }
    
    public function create() {
        $this->authController->checkAuth();
        $this->authController->requirePermission('create_request');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /DESL_2/fuel/request');
            exit;
        }
        
        $user = $this->authController->getCurrentUser();
        $errors = [];
        
        // التحقق من صحة البيانات
        $plateNumber = $_POST['plate_number'] ?? '';
        $stationId = $_POST['station_id'] ?? '';
        $fuelType = $_POST['fuel_type'] ?? '';
        $quantity = $_POST['quantity'] ?? '';
        $notes = $_POST['notes'] ?? '';
        
        if (empty($plateNumber)) {
            $errors[] = 'رقم اللوحة مطلوب';
        }
        
        if (empty($stationId)) {
            $errors[] = 'يجب اختيار محطة الوقود';
        }
        
        if (empty($fuelType)) {
            $errors[] = 'يجب اختيار نوع الوقود';
        }
        
        if (empty($quantity) || !is_numeric($quantity) || $quantity <= 0) {
            $errors[] = 'الكمية يجب أن تكون رقماً موجباً';
        }
        
        // البحث عن المركبة
        $vehicle = $this->vehicleModel->findByPlateNumber($plateNumber);
        if (!$vehicle) {
            $errors[] = 'المركبة غير مسجلة في النظام';
        }
        
        // التحقق من المحطة
        $station = $this->stationModel->findById($stationId);
        if (!$station) {
            $errors[] = 'المحطة المختارة غير صحيحة';
        }
        
        // التحقق من نوع الوقود للمركبة
        if ($vehicle && $vehicle['fuel_type'] !== $fuelType) {
            $errors[] = 'نوع الوقود المطلوب لا يتطابق مع نوع وقود المركبة';
        }
        
        // التحقق من الحصة المتاحة
        if ($vehicle) {
            $quotaStatus = $this->vehicleModel->getQuotaStatus($vehicle['id']);
            if ($quotaStatus['remaining'] < $quantity) {
                $errors[] = "الكمية المطلوبة ({$quantity} لتر) تتجاوز الحصة المتاحة ({$quotaStatus['remaining']} لتر)";
            }
        }
        
        if (!empty($errors)) {
            $this->showFormWithErrors($errors, $_POST);
            return;
        }
        
        try {
            // إنشاء الطلب
            $requestData = [
                'vehicle_id' => $vehicle['id'],
                'station_id' => $stationId,
                'user_id' => $user['id'],
                'fuel_type' => $fuelType,
                'quantity' => $quantity,
                'notes' => $notes
            ];
            
            $requestId = $this->fuelRequestModel->create($requestData);
            
            if ($requestId) {
                // تسجيل النشاط
                $userModel = new User();
                $userModel->logActivity($user['id'], 'إنشاء طلب وقود', 'fuel_requests', $requestId, null, $requestData);
                
                // إعادة التوجيه مع رسالة نجاح
                $_SESSION['success_message'] = 'تم إنشاء طلب الوقود بنجاح. رقم الطلب: ' . $requestId;
                header('Location: /DESL_2/fuel/request?success=1&request_id=' . $requestId);
                exit;
            } else {
                $errors[] = 'حدث خطأ أثناء إنشاء الطلب';
                $this->showFormWithErrors($errors, $_POST);
            }
            
        } catch (Exception $e) {
            $errors[] = $e->getMessage();
            $this->showFormWithErrors($errors, $_POST);
        }
    }
    
    public function approve() {
        $this->authController->checkAuth();
        $this->authController->requirePermission('manage_requests');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }
        
        $requestId = $_POST['request_id'] ?? '';
        $notes = $_POST['notes'] ?? '';
        
        if (empty($requestId)) {
            http_response_code(400);
            echo json_encode(['error' => 'معرف الطلب مطلوب']);
            return;
        }
        
        $request = $this->fuelRequestModel->findById($requestId);
        if (!$request) {
            http_response_code(404);
            echo json_encode(['error' => 'الطلب غير موجود']);
            return;
        }
        
        if ($request['status'] !== 'pending') {
            http_response_code(400);
            echo json_encode(['error' => 'لا يمكن الموافقة على طلب تم معالجته مسبقاً']);
            return;
        }
        
        try {
            // الموافقة على الطلب
            $result = $this->fuelRequestModel->updateStatus($requestId, 'approved', $notes);
            
            if ($result) {
                // إنشاء الخطاب الرسمي
                $letterId = $this->letterModel->create($requestId);
                
                $user = $this->authController->getCurrentUser();
                $userModel = new User();
                $userModel->logActivity($user['id'], 'الموافقة على طلب وقود', 'fuel_requests', $requestId);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'تم اعتماد الطلب وإنشاء الخطاب الرسمي',
                    'letter_id' => $letterId
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'حدث خطأ أثناء اعتماد الطلب']);
            }
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }
    
    public function reject() {
        $this->authController->checkAuth();
        $this->authController->requirePermission('manage_requests');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }
        
        $requestId = $_POST['request_id'] ?? '';
        $notes = $_POST['notes'] ?? '';
        
        if (empty($requestId)) {
            http_response_code(400);
            echo json_encode(['error' => 'معرف الطلب مطلوب']);
            return;
        }
        
        if (empty($notes)) {
            http_response_code(400);
            echo json_encode(['error' => 'سبب الرفض مطلوب']);
            return;
        }
        
        $request = $this->fuelRequestModel->findById($requestId);
        if (!$request) {
            http_response_code(404);
            echo json_encode(['error' => 'الطلب غير موجود']);
            return;
        }
        
        if ($request['status'] !== 'pending') {
            http_response_code(400);
            echo json_encode(['error' => 'لا يمكن رفض طلب تم معالجته مسبقاً']);
            return;
        }
        
        try {
            $result = $this->fuelRequestModel->updateStatus($requestId, 'rejected', $notes);
            
            if ($result) {
                $user = $this->authController->getCurrentUser();
                $userModel = new User();
                $userModel->logActivity($user['id'], 'رفض طلب وقود', 'fuel_requests', $requestId);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'تم رفض الطلب'
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'حدث خطأ أثناء رفض الطلب']);
            }
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }
    
    public function generateLetter() {
        $this->authController->checkAuth();
        
        $requestId = $_GET['request_id'] ?? '';
        
        if (empty($requestId)) {
            http_response_code(400);
            echo "معرف الطلب مطلوب";
            return;
        }
        
        $request = $this->fuelRequestModel->findById($requestId);
        if (!$request) {
            http_response_code(404);
            echo "الطلب غير موجود";
            return;
        }
        
        if ($request['status'] !== 'approved') {
            http_response_code(400);
            echo "لا يمكن إنشاء خطاب لطلب غير معتمد";
            return;
        }
        
        try {
            $letterId = $this->letterModel->create($requestId);
            
            if ($letterId) {
                $letter = $this->letterModel->findById($letterId);
                
                // إعادة توجيه لعرض الخطاب
                header("Location: /DESL_2/letter/view?id=$letterId");
                exit;
            } else {
                echo "حدث خطأ أثناء إنشاء الخطاب";
            }
            
        } catch (Exception $e) {
            echo "خطأ: " . $e->getMessage();
        }
    }
    
    private function showFormWithErrors($errors, $formData) {
        $user = $this->authController->getCurrentUser();
        $stations = $this->stationModel->getForSelect();
        $config = require CONFIG_PATH . '/app.php';
        $fuelTypes = $config['fuel_types'];
        
        $data = [
            'user' => $user,
            'stations' => $stations,
            'fuel_types' => $fuelTypes,
            'errors' => $errors,
            'form_data' => $formData,
            'page_title' => 'طلب صرف حصة وقود'
        ];
        
        $this->render('fuel/request', $data);
    }
    
    private function render($view, $data = []) {
        extract($data);
        
        $viewPath = VIEWS_PATH . '/' . $view . '.php';
        
        if (file_exists($viewPath)) {
            include $viewPath;
        } else {
            echo "العرض غير موجود: $view";
        }
    }
}
?>
