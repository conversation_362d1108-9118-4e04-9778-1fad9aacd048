<?php

class ReportController {
    private $authController;
    private $fuelRequestModel;
    private $vehicleModel;
    private $stationModel;
    private $letterModel;
    
    public function __construct() {
        $this->authController = new AuthController();
        $this->fuelRequestModel = new FuelRequest();
        $this->vehicleModel = new Vehicle();
        $this->stationModel = new Station();
        $this->letterModel = new Letter();
    }
    
    public function index() {
        $this->authController->checkAuth();
        $this->authController->requirePermission('view_reports');
        
        $user = $this->authController->getCurrentUser();
        
        // الحصول على المحطات للفلاتر
        $stations = $this->stationModel->getAll();
        
        // الحصول على أنواع الوقود
        $config = require CONFIG_PATH . '/app.php';
        $fuelTypes = $config['fuel_types'];
        $reportPeriods = $config['report_periods'];
        
        $data = [
            'user' => $user,
            'stations' => $stations,
            'fuel_types' => $fuelTypes,
            'report_periods' => $reportPeriods,
            'page_title' => 'التقارير والإحصائيات'
        ];
        
        $this->render('reports/index', $data);
    }
    
    public function generate() {
        $this->authController->checkAuth();
        $this->authController->requirePermission('view_reports');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /DESL_2/reports');
            exit;
        }
        
        $reportType = $_POST['report_type'] ?? '';
        $period = $_POST['period'] ?? 'monthly';
        $dateFrom = $_POST['date_from'] ?? '';
        $dateTo = $_POST['date_to'] ?? '';
        $stationId = $_POST['station_id'] ?? '';
        $fuelType = $_POST['fuel_type'] ?? '';
        $format = $_POST['format'] ?? 'html';
        
        $filters = [
            'period' => $period,
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'station_id' => $stationId,
            'fuel_type' => $fuelType
        ];
        
        try {
            switch ($reportType) {
                case 'fuel_consumption':
                    $reportData = $this->generateFuelConsumptionReport($filters);
                    break;
                    
                case 'station_activity':
                    $reportData = $this->generateStationActivityReport($filters);
                    break;
                    
                case 'vehicle_usage':
                    $reportData = $this->generateVehicleUsageReport($filters);
                    break;
                    
                case 'quota_analysis':
                    $reportData = $this->generateQuotaAnalysisReport($filters);
                    break;
                    
                case 'monthly_summary':
                    $reportData = $this->generateMonthlySummaryReport($filters);
                    break;
                    
                default:
                    throw new Exception('نوع التقرير غير صحيح');
            }
            
            // تسجيل النشاط
            $user = $this->authController->getCurrentUser();
            $userModel = new User();
            $userModel->logActivity($user['id'], "إنشاء تقرير: $reportType", 'reports');
            
            if ($format === 'pdf') {
                $this->generatePDFReport($reportData, $reportType);
            } elseif ($format === 'excel') {
                $this->generateExcelReport($reportData, $reportType);
            } else {
                $this->displayHTMLReport($reportData, $reportType, $filters);
            }
            
        } catch (Exception $e) {
            $_SESSION['errors'] = [$e->getMessage()];
            header('Location: /DESL_2/reports');
            exit;
        }
    }
    
    private function generateFuelConsumptionReport($filters) {
        $db = Database::getInstance()->getConnection();
        
        $whereConditions = ["fr.status = 'approved'"];
        $params = [];
        
        if (!empty($filters['date_from'])) {
            $whereConditions[] = "DATE(fr.request_date) >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $whereConditions[] = "DATE(fr.request_date) <= ?";
            $params[] = $filters['date_to'];
        }
        
        if (!empty($filters['station_id'])) {
            $whereConditions[] = "fr.station_id = ?";
            $params[] = $filters['station_id'];
        }
        
        if (!empty($filters['fuel_type'])) {
            $whereConditions[] = "fr.fuel_type = ?";
            $params[] = $filters['fuel_type'];
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        $stmt = $db->prepare("
            SELECT 
                fr.fuel_type,
                s.name as station_name,
                v.owner,
                COUNT(fr.id) as total_requests,
                SUM(fr.quantity) as total_fuel,
                AVG(fr.quantity) as avg_per_request,
                DATE(fr.request_date) as request_date
            FROM fuel_requests fr
            JOIN stations s ON fr.station_id = s.id
            JOIN vehicles v ON fr.vehicle_id = v.id
            WHERE $whereClause
            GROUP BY fr.fuel_type, s.name, v.owner, DATE(fr.request_date)
            ORDER BY request_date DESC, total_fuel DESC
        ");
        
        $stmt->execute($params);
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // إضافة إحصائيات إجمالية
        $totalStmt = $db->prepare("
            SELECT 
                COUNT(*) as total_requests,
                SUM(quantity) as total_fuel,
                COUNT(DISTINCT station_id) as stations_used,
                COUNT(DISTINCT vehicle_id) as vehicles_served
            FROM fuel_requests fr
            WHERE $whereClause
        ");
        
        $totalStmt->execute($params);
        $totals = $totalStmt->fetch(PDO::FETCH_ASSOC);
        
        return [
            'title' => 'تقرير استهلاك الوقود',
            'data' => $data,
            'totals' => $totals,
            'filters' => $filters
        ];
    }
    
    private function generateStationActivityReport($filters) {
        $db = Database::getInstance()->getConnection();
        
        $whereConditions = ["fr.status = 'approved'"];
        $params = [];
        
        if (!empty($filters['date_from'])) {
            $whereConditions[] = "DATE(fr.request_date) >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $whereConditions[] = "DATE(fr.request_date) <= ?";
            $params[] = $filters['date_to'];
        }
        
        if (!empty($filters['station_id'])) {
            $whereConditions[] = "fr.station_id = ?";
            $params[] = $filters['station_id'];
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        $stmt = $db->prepare("
            SELECT 
                s.name as station_name,
                s.location,
                s.code,
                COUNT(fr.id) as total_requests,
                SUM(fr.quantity) as total_fuel_dispensed,
                COUNT(DISTINCT fr.vehicle_id) as unique_vehicles,
                COUNT(DISTINCT v.owner) as unique_entities,
                AVG(fr.quantity) as avg_per_request,
                MIN(fr.request_date) as first_request,
                MAX(fr.request_date) as last_request
            FROM stations s
            LEFT JOIN fuel_requests fr ON s.id = fr.station_id AND $whereClause
            LEFT JOIN vehicles v ON fr.vehicle_id = v.id
            WHERE s.is_active = 1
            GROUP BY s.id, s.name, s.location, s.code
            ORDER BY total_fuel_dispensed DESC
        ");
        
        $stmt->execute($params);
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return [
            'title' => 'تقرير نشاط المحطات',
            'data' => $data,
            'filters' => $filters
        ];
    }
    
    private function generateVehicleUsageReport($filters) {
        $db = Database::getInstance()->getConnection();
        
        $whereConditions = ["fr.status = 'approved'"];
        $params = [];
        
        if (!empty($filters['date_from'])) {
            $whereConditions[] = "DATE(fr.request_date) >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $whereConditions[] = "DATE(fr.request_date) <= ?";
            $params[] = $filters['date_to'];
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        $stmt = $db->prepare("
            SELECT 
                v.plate_number,
                v.owner,
                v.vehicle_type,
                v.fuel_type,
                v.tank_capacity,
                COUNT(fr.id) as total_requests,
                SUM(fr.quantity) as total_fuel_consumed,
                AVG(fr.quantity) as avg_per_request,
                MIN(fr.request_date) as first_request,
                MAX(fr.request_date) as last_request,
                COUNT(DISTINCT fr.station_id) as stations_used
            FROM vehicles v
            LEFT JOIN fuel_requests fr ON v.id = fr.vehicle_id AND $whereClause
            WHERE v.is_active = 1
            GROUP BY v.id, v.plate_number, v.owner, v.vehicle_type, v.fuel_type, v.tank_capacity
            ORDER BY total_fuel_consumed DESC
        ");
        
        $stmt->execute($params);
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return [
            'title' => 'تقرير استخدام المركبات',
            'data' => $data,
            'filters' => $filters
        ];
    }
    
    private function generateQuotaAnalysisReport($filters) {
        $vehicles = $this->vehicleModel->getAll();
        $quotaData = [];
        
        foreach ($vehicles as $vehicle) {
            $quotaStatus = $this->vehicleModel->getQuotaStatus($vehicle['id']);
            $quotaData[] = array_merge($vehicle, $quotaStatus);
        }
        
        return [
            'title' => 'تقرير تحليل الحصص',
            'data' => $quotaData,
            'filters' => $filters
        ];
    }
    
    private function generateMonthlySummaryReport($filters) {
        $fuelStats = $this->fuelRequestModel->getStatistics('monthly');
        $vehicleStats = $this->vehicleModel->getStatistics();
        $letterStats = $this->letterModel->getStatistics('monthly');
        $monthlyTrends = $this->fuelRequestModel->getMonthlyTrends();
        
        return [
            'title' => 'التقرير الشهري الموجز',
            'fuel_stats' => $fuelStats,
            'vehicle_stats' => $vehicleStats,
            'letter_stats' => $letterStats,
            'monthly_trends' => $monthlyTrends,
            'filters' => $filters
        ];
    }
    
    private function generatePDFReport($reportData, $reportType) {
        // تنفيذ إنشاء PDF سيتم لاحقاً
        header('Content-Type: application/json');
        echo json_encode(['message' => 'PDF generation will be implemented']);
    }
    
    private function generateExcelReport($reportData, $reportType) {
        // تنفيذ إنشاء Excel سيتم لاحقاً
        header('Content-Type: application/json');
        echo json_encode(['message' => 'Excel generation will be implemented']);
    }
    
    private function displayHTMLReport($reportData, $reportType, $filters) {
        $user = $this->authController->getCurrentUser();
        
        $data = [
            'user' => $user,
            'report_data' => $reportData,
            'report_type' => $reportType,
            'filters' => $filters,
            'page_title' => $reportData['title']
        ];
        
        $this->render('reports/display', $data);
    }
    
    private function render($view, $data = []) {
        extract($data);
        
        $viewPath = VIEWS_PATH . '/' . $view . '.php';
        
        if (file_exists($viewPath)) {
            include $viewPath;
        } else {
            echo "العرض غير موجود: $view";
        }
    }
}
?>
