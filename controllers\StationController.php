<?php

class StationController {
    private $authController;
    private $stationModel;
    
    public function __construct() {
        $this->authController = new AuthController();
        $this->stationModel = new Station();
    }
    
    public function index() {
        $this->authController->checkAuth();
        
        $user = $this->authController->getCurrentUser();
        $stations = $this->stationModel->getAll();
        
        $data = [
            'user' => $user,
            'stations' => $stations,
            'page_title' => 'إدارة محطات الوقود'
        ];
        
        $this->render('stations/index', $data);
    }
    
    public function create() {
        $this->authController->checkAuth();
        $this->authController->requirePermission('manage_stations');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /DESL_2/stations');
            exit;
        }
        
        $errors = [];
        
        // التحقق من صحة البيانات
        $name = $_POST['name'] ?? '';
        $code = $_POST['code'] ?? '';
        $location = $_POST['location'] ?? '';
        $latitude = $_POST['latitude'] ?? null;
        $longitude = $_POST['longitude'] ?? null;
        $managedBy = $_POST['managed_by'] ?? '';
        
        if (empty($name)) {
            $errors[] = 'اسم المحطة مطلوب';
        }
        
        if (empty($code)) {
            $errors[] = 'كود المحطة مطلوب';
        }
        
        if (empty($location)) {
            $errors[] = 'موقع المحطة مطلوب';
        }
        
        if (empty($managedBy)) {
            $errors[] = 'نوع الإدارة مطلوب';
        }
        
        // التحقق من عدم تكرار الكود
        if (!empty($code)) {
            $existingStation = $this->stationModel->findByCode($code);
            if ($existingStation) {
                $errors[] = 'كود المحطة موجود مسبقاً';
            }
        }
        
        if (!empty($errors)) {
            $_SESSION['errors'] = $errors;
            $_SESSION['form_data'] = $_POST;
            header('Location: /DESL_2/stations');
            exit;
        }
        
        try {
            $stationData = [
                'name' => $name,
                'code' => $code,
                'location' => $location,
                'latitude' => !empty($latitude) ? $latitude : null,
                'longitude' => !empty($longitude) ? $longitude : null,
                'managed_by' => $managedBy
            ];
            
            $result = $this->stationModel->create($stationData);
            
            if ($result) {
                $user = $this->authController->getCurrentUser();
                $userModel = new User();
                $userModel->logActivity($user['id'], 'إضافة محطة وقود جديدة', 'stations', null, null, $stationData);
                
                $_SESSION['success_message'] = 'تم إضافة المحطة بنجاح';
            } else {
                $_SESSION['errors'] = ['حدث خطأ أثناء إضافة المحطة'];
            }
            
        } catch (Exception $e) {
            $_SESSION['errors'] = [$e->getMessage()];
        }
        
        header('Location: /DESL_2/stations');
        exit;
    }
    
    public function update() {
        $this->authController->checkAuth();
        $this->authController->requirePermission('manage_stations');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /DESL_2/stations');
            exit;
        }
        
        $stationId = $_POST['station_id'] ?? '';
        $errors = [];
        
        if (empty($stationId)) {
            $errors[] = 'معرف المحطة مطلوب';
        }
        
        $station = $this->stationModel->findById($stationId);
        if (!$station) {
            $errors[] = 'المحطة غير موجودة';
        }
        
        // التحقق من صحة البيانات
        $name = $_POST['name'] ?? '';
        $location = $_POST['location'] ?? '';
        $latitude = $_POST['latitude'] ?? null;
        $longitude = $_POST['longitude'] ?? null;
        $managedBy = $_POST['managed_by'] ?? '';
        
        if (empty($name)) {
            $errors[] = 'اسم المحطة مطلوب';
        }
        
        if (empty($location)) {
            $errors[] = 'موقع المحطة مطلوب';
        }
        
        if (empty($managedBy)) {
            $errors[] = 'نوع الإدارة مطلوب';
        }
        
        if (!empty($errors)) {
            $_SESSION['errors'] = $errors;
            header('Location: /DESL_2/stations');
            exit;
        }
        
        try {
            $updateData = [
                'name' => $name,
                'location' => $location,
                'latitude' => !empty($latitude) ? $latitude : null,
                'longitude' => !empty($longitude) ? $longitude : null,
                'managed_by' => $managedBy
            ];
            
            $result = $this->stationModel->update($stationId, $updateData);
            
            if ($result) {
                $user = $this->authController->getCurrentUser();
                $userModel = new User();
                $userModel->logActivity($user['id'], 'تحديث بيانات محطة وقود', 'stations', $stationId, $station, $updateData);
                
                $_SESSION['success_message'] = 'تم تحديث بيانات المحطة بنجاح';
            } else {
                $_SESSION['errors'] = ['حدث خطأ أثناء تحديث المحطة'];
            }
            
        } catch (Exception $e) {
            $_SESSION['errors'] = [$e->getMessage()];
        }
        
        header('Location: /DESL_2/stations');
        exit;
    }
    
    public function delete() {
        $this->authController->checkAuth();
        $this->authController->requirePermission('manage_stations');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }
        
        $stationId = $_POST['station_id'] ?? '';
        
        if (empty($stationId)) {
            http_response_code(400);
            echo json_encode(['error' => 'معرف المحطة مطلوب']);
            return;
        }
        
        $station = $this->stationModel->findById($stationId);
        if (!$station) {
            http_response_code(404);
            echo json_encode(['error' => 'المحطة غير موجودة']);
            return;
        }
        
        try {
            $result = $this->stationModel->delete($stationId);
            
            if ($result) {
                $user = $this->authController->getCurrentUser();
                $userModel = new User();
                $userModel->logActivity($user['id'], 'حذف محطة وقود', 'stations', $stationId, $station);
                
                echo json_encode(['success' => true, 'message' => 'تم حذف المحطة بنجاح']);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'حدث خطأ أثناء حذف المحطة']);
            }
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }
    
    public function getStations() {
        $this->authController->checkAuth();
        
        header('Content-Type: application/json');
        
        try {
            $stations = $this->stationModel->getForSelect();
            echo json_encode($stations);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }
    
    public function getStationDetails() {
        $this->authController->checkAuth();
        
        $stationId = $_GET['id'] ?? '';
        
        if (empty($stationId)) {
            http_response_code(400);
            echo json_encode(['error' => 'معرف المحطة مطلوب']);
            return;
        }
        
        try {
            $station = $this->stationModel->findById($stationId);
            
            if (!$station) {
                http_response_code(404);
                echo json_encode(['error' => 'المحطة غير موجودة']);
                return;
            }
            
            // إضافة إحصائيات المحطة
            $stats = $this->stationModel->getStationStatistics($stationId, 'monthly');
            $station['statistics'] = $stats;
            
            header('Content-Type: application/json');
            echo json_encode($station);
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }
    
    public function getNearbyStations() {
        $this->authController->checkAuth();
        
        $latitude = $_GET['lat'] ?? '';
        $longitude = $_GET['lng'] ?? '';
        $radius = $_GET['radius'] ?? 10;
        
        if (empty($latitude) || empty($longitude)) {
            http_response_code(400);
            echo json_encode(['error' => 'الإحداثيات مطلوبة']);
            return;
        }
        
        try {
            $stations = $this->stationModel->getNearbyStations($latitude, $longitude, $radius);
            
            header('Content-Type: application/json');
            echo json_encode($stations);
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }
    
    public function getStationRequests() {
        $this->authController->checkAuth();
        
        $stationId = $_GET['station_id'] ?? '';
        $status = $_GET['status'] ?? null;
        $limit = $_GET['limit'] ?? 20;
        
        if (empty($stationId)) {
            http_response_code(400);
            echo json_encode(['error' => 'معرف المحطة مطلوب']);
            return;
        }
        
        try {
            $requests = $this->stationModel->getStationRequests($stationId, $status, $limit);
            
            header('Content-Type: application/json');
            echo json_encode($requests);
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }
    
    private function render($view, $data = []) {
        extract($data);
        
        $viewPath = VIEWS_PATH . '/' . $view . '.php';
        
        if (file_exists($viewPath)) {
            include $viewPath;
        } else {
            echo "العرض غير موجود: $view";
        }
    }
}
?>
