<?php

class VehicleController {
    private $authController;
    private $vehicleModel;
    
    public function __construct() {
        $this->authController = new AuthController();
        $this->vehicleModel = new Vehicle();
    }
    
    public function index() {
        $this->authController->checkAuth();
        
        $user = $this->authController->getCurrentUser();
        $vehicles = $this->vehicleModel->getAll(50);
        
        // الحصول على أنواع الوقود
        $config = require CONFIG_PATH . '/app.php';
        $fuelTypes = $config['fuel_types'];
        
        $data = [
            'user' => $user,
            'vehicles' => $vehicles,
            'fuel_types' => $fuelTypes,
            'page_title' => 'إدارة المركبات'
        ];
        
        $this->render('vehicles/index', $data);
    }
    
    public function create() {
        $this->authController->checkAuth();
        $this->authController->requirePermission('manage_vehicles');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /DESL_2/vehicles');
            exit;
        }
        
        $errors = [];
        
        // التحقق من صحة البيانات
        $plateNumber = $_POST['plate_number'] ?? '';
        $chasisNumber = $_POST['chasis_number'] ?? '';
        $owner = $_POST['owner'] ?? '';
        $vehicleType = $_POST['vehicle_type'] ?? '';
        $fuelType = $_POST['fuel_type'] ?? '';
        $tankCapacity = $_POST['tank_capacity'] ?? '';
        
        if (empty($plateNumber)) {
            $errors[] = 'رقم اللوحة مطلوب';
        }
        
        if (empty($chasisNumber)) {
            $errors[] = 'رقم الشاسيه مطلوب';
        }
        
        if (empty($owner)) {
            $errors[] = 'الجهة المالكة مطلوبة';
        }
        
        if (empty($vehicleType)) {
            $errors[] = 'نوع المركبة مطلوب';
        }
        
        if (empty($fuelType)) {
            $errors[] = 'نوع الوقود مطلوب';
        }
        
        if (empty($tankCapacity) || !is_numeric($tankCapacity) || $tankCapacity <= 0) {
            $errors[] = 'سعة الخزان يجب أن تكون رقماً موجباً';
        }
        
        // التحقق من عدم تكرار رقم اللوحة
        if (!empty($plateNumber)) {
            $existingVehicle = $this->vehicleModel->findByPlateNumber($plateNumber);
            if ($existingVehicle) {
                $errors[] = 'رقم اللوحة موجود مسبقاً';
            }
        }
        
        if (!empty($errors)) {
            $_SESSION['errors'] = $errors;
            $_SESSION['form_data'] = $_POST;
            header('Location: /DESL_2/vehicles');
            exit;
        }
        
        try {
            $vehicleData = [
                'plate_number' => $plateNumber,
                'chasis_number' => $chasisNumber,
                'owner' => $owner,
                'vehicle_type' => $vehicleType,
                'fuel_type' => $fuelType,
                'tank_capacity' => $tankCapacity
            ];
            
            $result = $this->vehicleModel->create($vehicleData);
            
            if ($result) {
                $user = $this->authController->getCurrentUser();
                $userModel = new User();
                $userModel->logActivity($user['id'], 'إضافة مركبة جديدة', 'vehicles', null, null, $vehicleData);
                
                $_SESSION['success_message'] = 'تم إضافة المركبة بنجاح';
            } else {
                $_SESSION['errors'] = ['حدث خطأ أثناء إضافة المركبة'];
            }
            
        } catch (Exception $e) {
            $_SESSION['errors'] = [$e->getMessage()];
        }
        
        header('Location: /DESL_2/vehicles');
        exit;
    }
    
    public function update() {
        $this->authController->checkAuth();
        $this->authController->requirePermission('manage_vehicles');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /DESL_2/vehicles');
            exit;
        }
        
        $vehicleId = $_POST['vehicle_id'] ?? '';
        $errors = [];
        
        if (empty($vehicleId)) {
            $errors[] = 'معرف المركبة مطلوب';
        }
        
        $vehicle = $this->vehicleModel->findById($vehicleId);
        if (!$vehicle) {
            $errors[] = 'المركبة غير موجودة';
        }
        
        // التحقق من صحة البيانات
        $owner = $_POST['owner'] ?? '';
        $vehicleType = $_POST['vehicle_type'] ?? '';
        $fuelType = $_POST['fuel_type'] ?? '';
        $tankCapacity = $_POST['tank_capacity'] ?? '';
        
        if (empty($owner)) {
            $errors[] = 'الجهة المالكة مطلوبة';
        }
        
        if (empty($vehicleType)) {
            $errors[] = 'نوع المركبة مطلوب';
        }
        
        if (empty($fuelType)) {
            $errors[] = 'نوع الوقود مطلوب';
        }
        
        if (empty($tankCapacity) || !is_numeric($tankCapacity) || $tankCapacity <= 0) {
            $errors[] = 'سعة الخزان يجب أن تكون رقماً موجباً';
        }
        
        if (!empty($errors)) {
            $_SESSION['errors'] = $errors;
            header('Location: /DESL_2/vehicles');
            exit;
        }
        
        try {
            $updateData = [
                'owner' => $owner,
                'vehicle_type' => $vehicleType,
                'fuel_type' => $fuelType,
                'tank_capacity' => $tankCapacity
            ];
            
            $result = $this->vehicleModel->update($vehicleId, $updateData);
            
            if ($result) {
                $user = $this->authController->getCurrentUser();
                $userModel = new User();
                $userModel->logActivity($user['id'], 'تحديث بيانات مركبة', 'vehicles', $vehicleId, $vehicle, $updateData);
                
                $_SESSION['success_message'] = 'تم تحديث بيانات المركبة بنجاح';
            } else {
                $_SESSION['errors'] = ['حدث خطأ أثناء تحديث المركبة'];
            }
            
        } catch (Exception $e) {
            $_SESSION['errors'] = [$e->getMessage()];
        }
        
        header('Location: /DESL_2/vehicles');
        exit;
    }
    
    public function delete() {
        $this->authController->checkAuth();
        $this->authController->requirePermission('manage_vehicles');
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }
        
        $vehicleId = $_POST['vehicle_id'] ?? '';
        
        if (empty($vehicleId)) {
            http_response_code(400);
            echo json_encode(['error' => 'معرف المركبة مطلوب']);
            return;
        }
        
        $vehicle = $this->vehicleModel->findById($vehicleId);
        if (!$vehicle) {
            http_response_code(404);
            echo json_encode(['error' => 'المركبة غير موجودة']);
            return;
        }
        
        try {
            $result = $this->vehicleModel->delete($vehicleId);
            
            if ($result) {
                $user = $this->authController->getCurrentUser();
                $userModel = new User();
                $userModel->logActivity($user['id'], 'حذف مركبة', 'vehicles', $vehicleId, $vehicle);
                
                echo json_encode(['success' => true, 'message' => 'تم حذف المركبة بنجاح']);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'حدث خطأ أثناء حذف المركبة']);
            }
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }
    
    public function search() {
        $this->authController->checkAuth();
        
        $query = $_GET['q'] ?? '';
        
        if (empty($query)) {
            http_response_code(400);
            echo json_encode(['error' => 'استعلام البحث مطلوب']);
            return;
        }
        
        try {
            $vehicles = $this->vehicleModel->search($query);
            
            header('Content-Type: application/json');
            echo json_encode($vehicles);
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }
    
    public function getVehicleDetails() {
        $this->authController->checkAuth();
        
        $plateNumber = $_GET['plate_number'] ?? '';
        
        if (empty($plateNumber)) {
            http_response_code(400);
            echo json_encode(['error' => 'رقم اللوحة مطلوب']);
            return;
        }
        
        try {
            $vehicle = $this->vehicleModel->findByPlateNumber($plateNumber);
            
            if (!$vehicle) {
                http_response_code(404);
                echo json_encode(['error' => 'المركبة غير موجودة']);
                return;
            }
            
            // إضافة معلومات الحصة
            $quotaStatus = $this->vehicleModel->getQuotaStatus($vehicle['id']);
            $vehicle['quota_status'] = $quotaStatus;
            
            // إضافة الطلبات السابقة
            $fuelRequestModel = new FuelRequest();
            $recentRequests = $fuelRequestModel->getRequestsByVehicle($vehicle['id'], 5);
            $vehicle['recent_requests'] = $recentRequests;
            
            header('Content-Type: application/json');
            echo json_encode($vehicle);
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }
    
    public function getQuotaStatus() {
        $this->authController->checkAuth();
        
        $vehicleId = $_GET['vehicle_id'] ?? '';
        
        if (empty($vehicleId)) {
            http_response_code(400);
            echo json_encode(['error' => 'معرف المركبة مطلوب']);
            return;
        }
        
        try {
            $quotaStatus = $this->vehicleModel->getQuotaStatus($vehicleId);
            
            if (!$quotaStatus) {
                http_response_code(404);
                echo json_encode(['error' => 'المركبة غير موجودة']);
                return;
            }
            
            header('Content-Type: application/json');
            echo json_encode($quotaStatus);
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }
    
    private function render($view, $data = []) {
        extract($data);
        
        $viewPath = VIEWS_PATH . '/' . $view . '.php';
        
        if (file_exists($viewPath)) {
            include $viewPath;
        } else {
            echo "العرض غير موجود: $view";
        }
    }
}
?>
