<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// تحديد المسارات
define('ROOT_PATH', __DIR__);
define('CONFIG_PATH', ROOT_PATH . '/config');

// تحميل قاعدة البيانات
require_once CONFIG_PATH . '/database.php';

$user = [
    'id' => $_SESSION['user_id'],
    'name' => $_SESSION['user_name'],
    'role' => $_SESSION['user_role'],
    'email' => $_SESSION['user_email']
];

// الحصول على إحصائيات سريعة
try {
    $db = Database::getInstance()->getConnection();
    
    // عدد الطلبات اليوم
    $stmt = $db->prepare("SELECT COUNT(*) FROM fuel_requests WHERE DATE(request_date) = DATE('now')");
    $stmt->execute();
    $todayRequests = $stmt->fetchColumn();
    
    // الطلبات المعلقة
    $stmt = $db->prepare("SELECT COUNT(*) FROM fuel_requests WHERE status = 'pending'");
    $stmt->execute();
    $pendingRequests = $stmt->fetchColumn();
    
    // إجمالي الوقود اليوم
    $stmt = $db->prepare("SELECT SUM(quantity) FROM fuel_requests WHERE DATE(request_date) = DATE('now') AND status = 'approved'");
    $stmt->execute();
    $todayFuel = $stmt->fetchColumn() ?: 0;
    
    // عدد المحطات النشطة
    $stmt = $db->prepare("SELECT COUNT(*) FROM stations WHERE is_active = 1");
    $stmt->execute();
    $activeStations = $stmt->fetchColumn();
    
    // الطلبات الحديثة
    $stmt = $db->prepare("
        SELECT fr.*, v.plate_number, v.owner, s.name as station_name, u.name as requester_name
        FROM fuel_requests fr
        JOIN vehicles v ON fr.vehicle_id = v.id
        JOIN stations s ON fr.station_id = s.id
        JOIN users u ON fr.user_id = u.id
        ORDER BY fr.request_date DESC
        LIMIT 10
    ");
    $stmt->execute();
    $recentRequests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام صرف حصص الوقود</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .stats-card { transition: transform 0.3s ease; }
        .stats-card:hover { transform: translateY(-5px); }
        .navbar-brand { font-weight: 700; }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-gas-pump me-2"></i>
                نظام صرف حصص الوقود
            </a>
            
            <div class="navbar-nav me-auto">
                <a class="nav-link active" href="dashboard.php">
                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                </a>
                <a class="nav-link" href="fuel_request.php">
                    <i class="fas fa-plus-circle me-1"></i>طلب وقود
                </a>
            </div>
            
            <div class="navbar-nav">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        <?= htmlspecialchars($user['name']) ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><span class="dropdown-item-text">الدور: <?= htmlspecialchars($user['role']) ?></span></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="text-primary">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    لوحة التحكم الرئيسية
                </h2>
                <p class="text-muted">مرحباً <?= htmlspecialchars($user['name']) ?>، إليك نظرة عامة على النظام</p>
            </div>
        </div>

        <!-- الإحصائيات السريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">طلبات اليوم</h6>
                                <h3 class="mb-0"><?= $todayRequests ?></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-calendar-day fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card bg-warning text-white stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">طلبات معلقة</h6>
                                <h3 class="mb-0"><?= $pendingRequests ?></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card bg-success text-white stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">وقود اليوم (لتر)</h6>
                                <h3 class="mb-0"><?= number_format($todayFuel) ?></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-gas-pump fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card bg-info text-white stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">محطات نشطة</h6>
                                <h3 class="mb-0"><?= $activeStations ?></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-map-marker-alt fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- الطلبات الحديثة -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            الطلبات الحديثة
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($recentRequests)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>رقم اللوحة</th>
                                            <th>الجهة</th>
                                            <th>المحطة</th>
                                            <th>الكمية</th>
                                            <th>الحالة</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentRequests as $request): ?>
                                        <tr>
                                            <td><strong><?= htmlspecialchars($request['plate_number']) ?></strong></td>
                                            <td><?= htmlspecialchars($request['owner']) ?></td>
                                            <td><?= htmlspecialchars($request['station_name']) ?></td>
                                            <td><?= $request['quantity'] ?> لتر</td>
                                            <td>
                                                <?php
                                                $statusClass = [
                                                    'pending' => 'warning',
                                                    'approved' => 'success',
                                                    'rejected' => 'danger'
                                                ];
                                                $statusText = [
                                                    'pending' => 'معلق',
                                                    'approved' => 'معتمد',
                                                    'rejected' => 'مرفوض'
                                                ];
                                                ?>
                                                <span class="badge bg-<?= $statusClass[$request['status']] ?>">
                                                    <?= $statusText[$request['status']] ?>
                                                </span>
                                            </td>
                                            <td><?= date('Y-m-d H:i', strtotime($request['request_date'])) ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد طلبات حتى الآن</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- الإجراءات السريعة -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            الإجراءات السريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="fuel_request.php" class="btn btn-primary">
                                <i class="fas fa-plus-circle me-2"></i>
                                طلب وقود جديد
                            </a>
                            <a href="vehicles.php" class="btn btn-info">
                                <i class="fas fa-car me-2"></i>
                                إدارة المركبات
                            </a>
                            <a href="stations.php" class="btn btn-warning">
                                <i class="fas fa-gas-pump me-2"></i>
                                إدارة المحطات
                            </a>
                            <a href="reports.php" class="btn btn-secondary">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات النظام
                        </h5>
                    </div>
                    <div class="card-body">
                        <p><strong>الإصدار:</strong> 1.0.0</p>
                        <p><strong>آخر تحديث:</strong> <?= date('Y-m-d') ?></p>
                        <p><strong>حالة النظام:</strong> <span class="text-success">نشط</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
