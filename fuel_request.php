<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// تحديد المسارات
define('ROOT_PATH', __DIR__);
define('CONFIG_PATH', ROOT_PATH . '/config');

// تحميل قاعدة البيانات
require_once CONFIG_PATH . '/database.php';

$user = [
    'id' => $_SESSION['user_id'],
    'name' => $_SESSION['user_name'],
    'role' => $_SESSION['user_role'],
    'email' => $_SESSION['user_email']
];

$errors = [];
$success = '';

// معالجة إرسال الطلب
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $plateNumber = $_POST['plate_number'] ?? '';
    $stationId = $_POST['station_id'] ?? '';
    $fuelType = $_POST['fuel_type'] ?? '';
    $quantity = $_POST['quantity'] ?? '';
    $notes = $_POST['notes'] ?? '';
    
    // التحقق من البيانات
    if (empty($plateNumber)) {
        $errors[] = 'رقم اللوحة مطلوب';
    }
    
    if (empty($stationId)) {
        $errors[] = 'يجب اختيار محطة الوقود';
    }
    
    if (empty($fuelType)) {
        $errors[] = 'يجب اختيار نوع الوقود';
    }
    
    if (empty($quantity) || !is_numeric($quantity) || $quantity <= 0) {
        $errors[] = 'الكمية يجب أن تكون رقماً موجباً';
    }
    
    if (empty($errors)) {
        try {
            $db = Database::getInstance()->getConnection();
            
            // البحث عن المركبة
            $stmt = $db->prepare("SELECT * FROM vehicles WHERE plate_number = ? AND is_active = 1");
            $stmt->execute([$plateNumber]);
            $vehicle = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$vehicle) {
                $errors[] = 'المركبة غير مسجلة في النظام';
            } else {
                // إنشاء الطلب
                $stmt = $db->prepare("
                    INSERT INTO fuel_requests (vehicle_id, station_id, user_id, fuel_type, quantity, notes) 
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                
                $result = $stmt->execute([
                    $vehicle['id'],
                    $stationId,
                    $user['id'],
                    $fuelType,
                    $quantity,
                    $notes
                ]);
                
                if ($result) {
                    $success = 'تم إنشاء طلب الوقود بنجاح!';
                    // مسح البيانات
                    $_POST = [];
                } else {
                    $errors[] = 'حدث خطأ أثناء إنشاء الطلب';
                }
            }
        } catch (Exception $e) {
            $errors[] = 'حدث خطأ: ' . $e->getMessage();
        }
    }
}

// الحصول على المحطات
try {
    $db = Database::getInstance()->getConnection();
    $stmt = $db->prepare("SELECT * FROM stations WHERE is_active = 1 ORDER BY name");
    $stmt->execute();
    $stations = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $stations = [];
}

$fuelTypes = [
    'gasoline' => 'جازولين',
    'diesel' => 'ديزل',
    'kerosene' => 'كيروسين'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلب صرف حصة وقود - نظام صرف حصص الوقود</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .form-card { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-gas-pump me-2"></i>
                نظام صرف حصص الوقود
            </a>
            
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-arrow-right me-1"></i>العودة للوحة التحكم
                </a>
            </div>
            
            <div class="navbar-nav">
                <span class="navbar-text">
                    <i class="fas fa-user me-1"></i>
                    <?= htmlspecialchars($user['name']) ?>
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h2 class="card-title mb-0">
                            <i class="fas fa-plus-circle me-2"></i>
                            طلب صرف حصة وقود جديد
                        </h2>
                        <p class="card-text mt-2">املأ البيانات المطلوبة لإنشاء طلب صرف وقود</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- رسائل الأخطاء والنجاح -->
        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>يرجى تصحيح الأخطاء التالية:</strong>
                <ul class="mb-0 mt-2">
                    <?php foreach ($errors as $error): ?>
                        <li><?= htmlspecialchars($error) ?></li>
                    <?php endforeach; ?>
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($success) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <form method="POST" action="">
            <div class="row">
                <!-- بيانات المركبة -->
                <div class="col-md-6">
                    <div class="card form-card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-car me-2"></i>
                                بيانات المركبة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="plate_number" class="form-label">
                                    <i class="fas fa-id-card me-1"></i>
                                    رقم اللوحة *
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="plate_number" 
                                       name="plate_number" 
                                       placeholder="مثال: س و 1234"
                                       value="<?= htmlspecialchars($_POST['plate_number'] ?? '') ?>"
                                       required>
                                <small class="form-text text-muted">أدخل رقم لوحة المركبة</small>
                            </div>

                            <div class="mb-3">
                                <label for="fuel_type" class="form-label">
                                    <i class="fas fa-oil-can me-1"></i>
                                    نوع الوقود *
                                </label>
                                <select class="form-select" id="fuel_type" name="fuel_type" required>
                                    <option value="">اختر نوع الوقود</option>
                                    <?php foreach ($fuelTypes as $key => $name): ?>
                                        <option value="<?= $key ?>" <?= (($_POST['fuel_type'] ?? '') === $key) ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($name) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="quantity" class="form-label">
                                    <i class="fas fa-tachometer-alt me-1"></i>
                                    الكمية المطلوبة (لتر) *
                                </label>
                                <input type="number" 
                                       class="form-control" 
                                       id="quantity" 
                                       name="quantity" 
                                       min="1" 
                                       max="1000"
                                       placeholder="أدخل الكمية بالليتر"
                                       value="<?= htmlspecialchars($_POST['quantity'] ?? '') ?>"
                                       required>
                                <div class="form-text">الحد الأقصى: 1000 لتر</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- اختيار المحطة -->
                <div class="col-md-6">
                    <div class="card form-card mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                اختيار محطة الوقود
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="station_id" class="form-label">
                                    <i class="fas fa-gas-pump me-1"></i>
                                    المحطة *
                                </label>
                                <select class="form-select" id="station_id" name="station_id" required>
                                    <option value="">اختر محطة الوقود</option>
                                    <?php foreach ($stations as $station): ?>
                                        <option value="<?= $station['id'] ?>" <?= (($_POST['station_id'] ?? '') == $station['id']) ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($station['name']) ?> - <?= htmlspecialchars($station['location']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">
                                    <i class="fas fa-sticky-note me-1"></i>
                                    ملاحظات إضافية
                                </label>
                                <textarea class="form-control" 
                                          id="notes" 
                                          name="notes" 
                                          rows="3"
                                          placeholder="أي ملاحظات إضافية..."><?= htmlspecialchars($_POST['notes'] ?? '') ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="card form-card">
                <div class="card-body text-center">
                    <button type="submit" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-paper-plane me-2"></i>
                        إرسال الطلب
                    </button>
                    <a href="dashboard.php" class="btn btn-secondary btn-lg">
                        <i class="fas fa-times me-2"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
