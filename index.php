<?php
session_start();

// تحديد المسارات الأساسية
define('ROOT_PATH', __DIR__);
define('CONFIG_PATH', ROOT_PATH . '/config');
define('MODELS_PATH', ROOT_PATH . '/models');
define('CONTROLLERS_PATH', ROOT_PATH . '/controllers');
define('VIEWS_PATH', ROOT_PATH . '/views');
define('ASSETS_PATH', ROOT_PATH . '/assets');
define('VENDOR_PATH', ROOT_PATH . '/vendor');

// تحميل الإعدادات
require_once CONFIG_PATH . '/app.php';
require_once CONFIG_PATH . '/database.php';

// تحميل الكلاسات الأساسية
spl_autoload_register(function ($class) {
    $paths = [
        MODELS_PATH . '/' . $class . '.php',
        CONTROLLERS_PATH . '/' . $class . '.php',
    ];
    
    foreach ($paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            break;
        }
    }
});

// معالج الطلبات الأساسي
class Router {
    private $routes = [];
    
    public function get($path, $callback) {
        $this->routes['GET'][$path] = $callback;
    }
    
    public function post($path, $callback) {
        $this->routes['POST'][$path] = $callback;
    }
    
    public function dispatch() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $path = str_replace('/DESL_2', '', $path); // إزالة مسار المشروع
        
        if ($path === '' || $path === '/') {
            $path = '/dashboard';
        }
        
        if (isset($this->routes[$method][$path])) {
            $callback = $this->routes[$method][$path];
            if (is_callable($callback)) {
                call_user_func($callback);
            } else {
                list($controller, $method) = explode('@', $callback);
                $controllerInstance = new $controller();
                $controllerInstance->$method();
            }
        } else {
            http_response_code(404);
            echo "الصفحة غير موجودة";
        }
    }
}

// إنشاء الموجه
$router = new Router();

// تعريف المسارات
$router->get('/login', 'AuthController@showLogin');
$router->post('/login', 'AuthController@login');
$router->get('/logout', 'AuthController@logout');
$router->get('/dashboard', 'DashboardController@index');
$router->get('/fuel/request', 'FuelRequestController@showForm');
$router->post('/fuel/request', 'FuelRequestController@create');
$router->get('/stations', 'StationController@index');
$router->post('/stations', 'StationController@create');
$router->get('/vehicles', 'VehicleController@index');
$router->post('/vehicles', 'VehicleController@create');
$router->get('/reports', 'ReportController@index');
$router->get('/api/stations', 'StationController@getStations');
$router->get('/api/vehicles/search', 'VehicleController@search');
$router->get('/letter/generate', 'FuelRequestController@generateLetter');

// تشغيل الموجه
$router->dispatch();
?>
