<?php
// تفعيل عرض الأخطاء للتطوير
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// تحديد المسارات الأساسية
define('ROOT_PATH', __DIR__);
define('CONFIG_PATH', ROOT_PATH . '/config');
define('MODELS_PATH', ROOT_PATH . '/models');
define('CONTROLLERS_PATH', ROOT_PATH . '/controllers');
define('VIEWS_PATH', ROOT_PATH . '/views');
define('ASSETS_PATH', ROOT_PATH . '/assets');
define('VENDOR_PATH', ROOT_PATH . '/vendor');

// التحقق من وجود الملفات المطلوبة
if (!file_exists(CONFIG_PATH . '/app.php')) {
    die('ملف الإعدادات غير موجود: ' . CONFIG_PATH . '/app.php');
}

if (!file_exists(CONFIG_PATH . '/database.php')) {
    die('ملف قاعدة البيانات غير موجود: ' . CONFIG_PATH . '/database.php');
}

// تحميل الإعدادات
require_once CONFIG_PATH . '/app.php';
require_once CONFIG_PATH . '/database.php';

// تحميل الكلاسات الأساسية
spl_autoload_register(function ($class) {
    $paths = [
        MODELS_PATH . '/' . $class . '.php',
        CONTROLLERS_PATH . '/' . $class . '.php',
    ];
    
    foreach ($paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            break;
        }
    }
});

// معالج الطلبات الأساسي
class Router {
    private $routes = [];
    
    public function get($path, $callback) {
        $this->routes['GET'][$path] = $callback;
    }
    
    public function post($path, $callback) {
        $this->routes['POST'][$path] = $callback;
    }
    
    public function dispatch() {
        try {
            $method = $_SERVER['REQUEST_METHOD'];
            $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
            $path = str_replace('/DESL_2', '', $path); // إزالة مسار المشروع

            if ($path === '' || $path === '/') {
                $path = '/login'; // توجيه إلى صفحة تسجيل الدخول بدلاً من لوحة التحكم
            }

            if (isset($this->routes[$method][$path])) {
                $callback = $this->routes[$method][$path];
                if (is_callable($callback)) {
                    call_user_func($callback);
                } else {
                    list($controller, $methodName) = explode('@', $callback);

                    // التحقق من وجود الكلاس
                    if (!class_exists($controller)) {
                        throw new Exception("Controller not found: $controller");
                    }

                    $controllerInstance = new $controller();

                    // التحقق من وجود الدالة
                    if (!method_exists($controllerInstance, $methodName)) {
                        throw new Exception("Method not found: $controller::$methodName");
                    }

                    $controllerInstance->$methodName();
                }
            } else {
                http_response_code(404);
                echo "<h1>404 - الصفحة غير موجودة</h1>";
                echo "<p>المسار المطلوب: $path</p>";
                echo "<p>الطريقة: $method</p>";
                echo "<a href='/DESL_2/login'>العودة لتسجيل الدخول</a>";
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo "<h1>خطأ في الخادم</h1>";
            echo "<p>الرسالة: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<p>الملف: " . $e->getFile() . "</p>";
            echo "<p>السطر: " . $e->getLine() . "</p>";
        }
    }
}

// إنشاء الموجه
$router = new Router();

// تعريف المسارات
$router->get('/login', 'AuthController@showLogin');
$router->post('/login', 'AuthController@login');
$router->get('/logout', 'AuthController@logout');
$router->get('/dashboard', 'DashboardController@index');
$router->get('/fuel/request', 'FuelRequestController@showForm');
$router->post('/fuel/request', 'FuelRequestController@create');
$router->get('/stations', 'StationController@index');
$router->post('/stations', 'StationController@create');
$router->get('/vehicles', 'VehicleController@index');
$router->post('/vehicles', 'VehicleController@create');
$router->get('/reports', 'ReportController@index');
$router->get('/api/stations', 'StationController@getStations');
$router->get('/api/vehicles/search', 'VehicleController@search');
$router->get('/letter/generate', 'FuelRequestController@generateLetter');

// تشغيل الموجه
$router->dispatch();
?>
