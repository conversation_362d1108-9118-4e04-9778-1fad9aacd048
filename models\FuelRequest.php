<?php

class FuelRequest {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    public function create($data) {
        // التحقق من الحصة المتاحة
        $vehicle = new Vehicle();
        $quotaStatus = $vehicle->getQuotaStatus($data['vehicle_id']);
        
        if ($quotaStatus['remaining'] < $data['quantity']) {
            throw new Exception('الكمية المطلوبة تتجاوز الحصة المتاحة');
        }
        
        // التحقق من عدم تكرار الطلب في نفس اليوم
        if ($this->isDuplicateRequest($data['vehicle_id'], $data['station_id'])) {
            throw new Exception('يوجد طلب مشابه لنفس المركبة في نفس المحطة اليوم');
        }
        
        $stmt = $this->db->prepare("
            INSERT INTO fuel_requests (vehicle_id, station_id, user_id, fuel_type, quantity, notes) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            $data['vehicle_id'],
            $data['station_id'],
            $data['user_id'],
            $data['fuel_type'],
            $data['quantity'],
            $data['notes'] ?? null
        ]);
        
        if ($result) {
            return $this->db->lastInsertId();
        }
        
        return false;
    }
    
    public function findById($id) {
        $stmt = $this->db->prepare("
            SELECT 
                fr.*,
                v.plate_number,
                v.chasis_number,
                v.owner,
                v.vehicle_type,
                s.name as station_name,
                s.code as station_code,
                s.location as station_location,
                u.name as requester_name
            FROM fuel_requests fr
            JOIN vehicles v ON fr.vehicle_id = v.id
            JOIN stations s ON fr.station_id = s.id
            JOIN users u ON fr.user_id = u.id
            WHERE fr.id = ?
        ");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function getAll($filters = []) {
        $whereConditions = [];
        $params = [];
        
        if (!empty($filters['status'])) {
            $whereConditions[] = "fr.status = ?";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['station_id'])) {
            $whereConditions[] = "fr.station_id = ?";
            $params[] = $filters['station_id'];
        }
        
        if (!empty($filters['user_id'])) {
            $whereConditions[] = "fr.user_id = ?";
            $params[] = $filters['user_id'];
        }
        
        if (!empty($filters['date_from'])) {
            $whereConditions[] = "DATE(fr.request_date) >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $whereConditions[] = "DATE(fr.request_date) <= ?";
            $params[] = $filters['date_to'];
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        $stmt = $this->db->prepare("
            SELECT 
                fr.*,
                v.plate_number,
                v.owner,
                s.name as station_name,
                u.name as requester_name
            FROM fuel_requests fr
            JOIN vehicles v ON fr.vehicle_id = v.id
            JOIN stations s ON fr.station_id = s.id
            JOIN users u ON fr.user_id = u.id
            $whereClause
            ORDER BY fr.request_date DESC
        ");
        
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function updateStatus($id, $status, $notes = null) {
        $stmt = $this->db->prepare("
            UPDATE fuel_requests 
            SET status = ?, notes = ?, approved_date = CASE WHEN ? = 'approved' THEN CURRENT_TIMESTAMP ELSE approved_date END
            WHERE id = ?
        ");
        
        return $stmt->execute([$status, $notes, $status, $id]);
    }
    
    public function getRequestsByVehicle($vehicleId, $limit = 10) {
        $stmt = $this->db->prepare("
            SELECT 
                fr.*,
                s.name as station_name,
                u.name as requester_name
            FROM fuel_requests fr
            JOIN stations s ON fr.station_id = s.id
            JOIN users u ON fr.user_id = u.id
            WHERE fr.vehicle_id = ?
            ORDER BY fr.request_date DESC
            LIMIT ?
        ");
        
        $stmt->execute([$vehicleId, $limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getRequestsByUser($userId, $limit = 20) {
        $stmt = $this->db->prepare("
            SELECT 
                fr.*,
                v.plate_number,
                v.owner,
                s.name as station_name
            FROM fuel_requests fr
            JOIN vehicles v ON fr.vehicle_id = v.id
            JOIN stations s ON fr.station_id = s.id
            WHERE fr.user_id = ?
            ORDER BY fr.request_date DESC
            LIMIT ?
        ");
        
        $stmt->execute([$userId, $limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getPendingRequests($limit = 50) {
        $stmt = $this->db->prepare("
            SELECT 
                fr.*,
                v.plate_number,
                v.owner,
                s.name as station_name,
                u.name as requester_name
            FROM fuel_requests fr
            JOIN vehicles v ON fr.vehicle_id = v.id
            JOIN stations s ON fr.station_id = s.id
            JOIN users u ON fr.user_id = u.id
            WHERE fr.status = 'pending'
            ORDER BY fr.request_date ASC
            LIMIT ?
        ");
        
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getStatistics($period = 'monthly') {
        $dateCondition = '';
        switch ($period) {
            case 'daily':
                $dateCondition = "WHERE DATE(request_date) = DATE('now')";
                break;
            case 'weekly':
                $dateCondition = "WHERE DATE(request_date) >= DATE('now', '-7 days')";
                break;
            case 'monthly':
                $dateCondition = "WHERE DATE(request_date) >= DATE('now', 'start of month')";
                break;
            case 'yearly':
                $dateCondition = "WHERE DATE(request_date) >= DATE('now', 'start of year')";
                break;
        }
        
        $stmt = $this->db->prepare("
            SELECT 
                COUNT(*) as total_requests,
                COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_requests,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_requests,
                COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_requests,
                SUM(CASE WHEN status = 'approved' THEN quantity ELSE 0 END) as total_fuel_approved,
                SUM(CASE WHEN status = 'approved' AND fuel_type = 'gasoline' THEN quantity ELSE 0 END) as gasoline_approved,
                SUM(CASE WHEN status = 'approved' AND fuel_type = 'diesel' THEN quantity ELSE 0 END) as diesel_approved,
                AVG(CASE WHEN status = 'approved' THEN quantity END) as avg_quantity_per_request
            FROM fuel_requests 
            $dateCondition
        ");
        
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function isDuplicateRequest($vehicleId, $stationId) {
        $stmt = $this->db->prepare("
            SELECT COUNT(*) 
            FROM fuel_requests 
            WHERE vehicle_id = ? 
            AND station_id = ? 
            AND DATE(request_date) = DATE('now')
            AND status IN ('pending', 'approved')
        ");
        
        $stmt->execute([$vehicleId, $stationId]);
        return $stmt->fetchColumn() > 0;
    }
    
    public function generateRequestNumber() {
        $date = date('Ymd');
        $stmt = $this->db->prepare("
            SELECT COUNT(*) + 1 as next_number 
            FROM fuel_requests 
            WHERE DATE(request_date) = DATE('now')
        ");
        $stmt->execute();
        $nextNumber = $stmt->fetchColumn();
        
        return "FUEL-{$date}-" . str_pad($nextNumber, 3, '0', STR_PAD_LEFT);
    }
    
    public function getMonthlyTrends() {
        $stmt = $this->db->prepare("
            SELECT 
                strftime('%Y-%m', request_date) as month,
                COUNT(*) as total_requests,
                SUM(CASE WHEN status = 'approved' THEN quantity ELSE 0 END) as total_fuel,
                fuel_type
            FROM fuel_requests 
            WHERE request_date >= DATE('now', '-12 months')
            GROUP BY strftime('%Y-%m', request_date), fuel_type
            ORDER BY month DESC
        ");
        
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
