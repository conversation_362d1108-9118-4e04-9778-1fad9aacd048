<?php

class Letter {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    public function create($requestId) {
        // الحصول على بيانات الطلب
        $fuelRequest = new FuelRequest();
        $request = $fuelRequest->findById($requestId);
        
        if (!$request || $request['status'] !== 'approved') {
            throw new Exception('لا يمكن إنشاء خطاب لطلب غير معتمد');
        }
        
        // توليد رقم الخطاب
        $letterNumber = $this->generateLetterNumber();
        
        // توليد QR Hash
        $qrHash = $this->generateQRHash($requestId, $letterNumber);
        
        $stmt = $this->db->prepare("
            INSERT INTO letters (request_id, letter_number, qr_hash) 
            VALUES (?, ?, ?)
        ");
        
        $result = $stmt->execute([$requestId, $letterNumber, $qrHash]);
        
        if ($result) {
            $letterId = $this->db->lastInsertId();
            
            // توليد ملف PDF
            $pdfPath = $this->generatePDF($letterId);
            
            // تحديث مسار PDF في قاعدة البيانات
            $this->updatePDFPath($letterId, $pdfPath);
            
            return $letterId;
        }
        
        return false;
    }
    
    public function findById($id) {
        $stmt = $this->db->prepare("
            SELECT 
                l.*,
                fr.quantity,
                fr.fuel_type,
                v.plate_number,
                v.chasis_number,
                v.owner,
                s.name as station_name,
                s.location as station_location,
                u.name as requester_name
            FROM letters l
            JOIN fuel_requests fr ON l.request_id = fr.id
            JOIN vehicles v ON fr.vehicle_id = v.id
            JOIN stations s ON fr.station_id = s.id
            JOIN users u ON fr.user_id = u.id
            WHERE l.id = ?
        ");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function findByLetterNumber($letterNumber) {
        $stmt = $this->db->prepare("
            SELECT 
                l.*,
                fr.quantity,
                fr.fuel_type,
                v.plate_number,
                v.owner,
                s.name as station_name
            FROM letters l
            JOIN fuel_requests fr ON l.request_id = fr.id
            JOIN vehicles v ON fr.vehicle_id = v.id
            JOIN stations s ON fr.station_id = s.id
            WHERE l.letter_number = ?
        ");
        $stmt->execute([$letterNumber]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function markAsPrinted($id) {
        $stmt = $this->db->prepare("
            UPDATE letters 
            SET printed_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        ");
        return $stmt->execute([$id]);
    }
    
    public function markAsUsed($id) {
        $stmt = $this->db->prepare("
            UPDATE letters 
            SET is_used = 1 
            WHERE id = ?
        ");
        return $stmt->execute([$id]);
    }
    
    public function verifyQR($qrHash) {
        $stmt = $this->db->prepare("
            SELECT 
                l.*,
                fr.quantity,
                fr.fuel_type,
                v.plate_number,
                v.owner,
                s.name as station_name
            FROM letters l
            JOIN fuel_requests fr ON l.request_id = fr.id
            JOIN vehicles v ON fr.vehicle_id = v.id
            JOIN stations s ON fr.station_id = s.id
            WHERE l.qr_hash = ? AND l.is_used = 0
        ");
        $stmt->execute([$qrHash]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function generateLetterNumber() {
        $config = require CONFIG_PATH . '/app.php';
        $prefix = $config['letter_settings']['letter_prefix'];
        $date = date('Ymd');
        
        $stmt = $this->db->prepare("
            SELECT COUNT(*) + 1 as next_number 
            FROM letters 
            WHERE DATE(created_at) = DATE('now')
        ");
        $stmt->execute();
        $nextNumber = $stmt->fetchColumn();
        
        return "{$prefix}-{$date}-" . str_pad($nextNumber, 3, '0', STR_PAD_LEFT);
    }
    
    private function generateQRHash($requestId, $letterNumber) {
        $data = $requestId . $letterNumber . time();
        return hash('sha256', $data);
    }
    
    private function generatePDF($letterId) {
        $letter = $this->findById($letterId);
        if (!$letter) return false;
        
        // تحميل مكتبة FPDF
        require_once VENDOR_PATH . '/fpdf/fpdf.php';
        
        $pdf = new FPDF('P', 'mm', 'A4');
        $pdf->AddPage();
        
        // إضافة الخط العربي (يحتاج إلى خط يدعم العربية)
        $pdf->SetFont('Arial', 'B', 16);
        
        // عنوان الوزارة
        $config = require CONFIG_PATH . '/app.php';
        $ministryName = $config['letter_settings']['ministry_name'];
        
        $pdf->Cell(0, 10, $ministryName, 0, 1, 'C');
        $pdf->Ln(10);
        
        // معلومات الخطاب
        $pdf->SetFont('Arial', '', 12);
        $pdf->Cell(0, 8, 'Letter Number: ' . $letter['letter_number'], 0, 1, 'R');
        $pdf->Cell(0, 8, 'Date: ' . date('Y-m-d'), 0, 1, 'R');
        $pdf->Ln(10);
        
        // محتوى الخطاب
        $pdf->Cell(0, 8, 'To: ' . $letter['station_name'], 0, 1);
        $pdf->Ln(5);
        
        $fuelTypes = $config['fuel_types'];
        $fuelTypeName = $fuelTypes[$letter['fuel_type']] ?? $letter['fuel_type'];
        
        $content = "Please dispense {$letter['quantity']} liters of {$fuelTypeName} ";
        $content .= "for vehicle belonging to {$letter['owner']}.\n";
        $content .= "Plate Number: {$letter['plate_number']}\n";
        $content .= "Chassis Number: {$letter['chasis_number']}\n\n";
        $content .= "This letter is electronically approved. ";
        $content .= "Fuel should only be dispensed after QR code verification.";
        
        $pdf->MultiCell(0, 6, $content);
        $pdf->Ln(10);
        
        // QR Code (نص بسيط بدلاً من QR فعلي لهذا المثال)
        $pdf->Cell(0, 8, 'QR Code: ' . substr($letter['qr_hash'], 0, 16) . '...', 0, 1, 'C');
        $pdf->Ln(10);
        
        // التوقيع والختم
        $pdf->Cell(0, 8, 'Ministry Seal', 0, 1, 'C');
        
        // حفظ الملف
        $fileName = 'letter_' . $letter['letter_number'] . '.pdf';
        $filePath = ROOT_PATH . '/storage/letters/' . $fileName;
        
        // إنشاء مجلد التخزين إذا لم يكن موجوداً
        if (!file_exists(dirname($filePath))) {
            mkdir(dirname($filePath), 0755, true);
        }
        
        $pdf->Output('F', $filePath);
        
        return 'storage/letters/' . $fileName;
    }
    
    private function updatePDFPath($letterId, $pdfPath) {
        $stmt = $this->db->prepare("UPDATE letters SET generated_pdf = ? WHERE id = ?");
        return $stmt->execute([$pdfPath, $letterId]);
    }
    
    public function getAll($filters = []) {
        $whereConditions = [];
        $params = [];
        
        if (!empty($filters['station_id'])) {
            $whereConditions[] = "fr.station_id = ?";
            $params[] = $filters['station_id'];
        }
        
        if (!empty($filters['date_from'])) {
            $whereConditions[] = "DATE(l.created_at) >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $whereConditions[] = "DATE(l.created_at) <= ?";
            $params[] = $filters['date_to'];
        }
        
        if (isset($filters['is_used'])) {
            $whereConditions[] = "l.is_used = ?";
            $params[] = $filters['is_used'];
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        $stmt = $this->db->prepare("
            SELECT 
                l.*,
                fr.quantity,
                fr.fuel_type,
                v.plate_number,
                v.owner,
                s.name as station_name,
                u.name as requester_name
            FROM letters l
            JOIN fuel_requests fr ON l.request_id = fr.id
            JOIN vehicles v ON fr.vehicle_id = v.id
            JOIN stations s ON fr.station_id = s.id
            JOIN users u ON fr.user_id = u.id
            $whereClause
            ORDER BY l.created_at DESC
        ");
        
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getStatistics($period = 'monthly') {
        $dateCondition = '';
        switch ($period) {
            case 'daily':
                $dateCondition = "WHERE DATE(l.created_at) = DATE('now')";
                break;
            case 'weekly':
                $dateCondition = "WHERE DATE(l.created_at) >= DATE('now', '-7 days')";
                break;
            case 'monthly':
                $dateCondition = "WHERE DATE(l.created_at) >= DATE('now', 'start of month')";
                break;
            case 'yearly':
                $dateCondition = "WHERE DATE(l.created_at) >= DATE('now', 'start of year')";
                break;
        }
        
        $stmt = $this->db->prepare("
            SELECT 
                COUNT(*) as total_letters,
                COUNT(CASE WHEN printed_at IS NOT NULL THEN 1 END) as printed_letters,
                COUNT(CASE WHEN is_used = 1 THEN 1 END) as used_letters,
                COUNT(CASE WHEN is_used = 0 THEN 1 END) as unused_letters
            FROM letters l
            $dateCondition
        ");
        
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
?>
