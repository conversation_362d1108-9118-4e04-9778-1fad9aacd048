<?php

require_once VENDOR_PATH . '/fpdf/fpdf.php';

/**
 * مولد الخطابات الرسمية السيادية
 * وزارة المالية والتخطيط الاقتصادي - جمهورية السودان
 */

class SovereignLetterGenerator extends FPDF {
    private $config;
    private $sovereignSignature;
    private $letterData;
    
    public function __construct() {
        parent::__construct('P', 'mm', 'A4');
        $this->config = require CONFIG_PATH . '/sovereign.php';
        $this->sovereignSignature = new SovereignSignature();
    }
    
    /**
     * إنشاء خطاب رسمي سيادي
     */
    public function generateSovereignLetter($requestData) {
        $this->letterData = $this->prepareLetterData($requestData);
        
        $this->AddPage();
        $this->SetAutoPageBreak(true, 20);
        
        // رأس الخطاب السيادي
        $this->addSovereignHeader();
        
        // معلومات الخطاب
        $this->addLetterInfo();
        
        // محتوى الخطاب
        $this->addLetterContent();
        
        // التوقيعات والأختام
        $this->addSignaturesAndSeals();
        
        // رمز QR للتحقق
        $this->addVerificationQR();
        
        // تذييل الخطاب
        $this->addSovereignFooter();
        
        return $this->generatePDF();
    }
    
    /**
     * إضافة رأس الخطاب السيادي
     */
    private function addSovereignHeader() {
        // شعار الدولة
        $this->SetY(10);
        $this->SetX(85);
        
        // عنوان الدولة
        $this->SetFont('Arial', 'B', 16);
        $this->SetTextColor(0, 0, 0);
        $this->Cell(0, 8, iconv('UTF-8', 'windows-1256', $this->config['sovereign_info']['country']), 0, 1, 'C');
        
        $this->SetFont('Arial', 'B', 14);
        $this->Cell(0, 6, iconv('UTF-8', 'windows-1256', $this->config['sovereign_info']['ministry']), 0, 1, 'C');
        
        $this->SetFont('Arial', '', 12);
        $this->Cell(0, 5, iconv('UTF-8', 'windows-1256', $this->config['sovereign_info']['department']), 0, 1, 'C');
        
        // خط فاصل
        $this->Ln(5);
        $this->SetDrawColor(0, 0, 0);
        $this->Line(20, $this->GetY(), 190, $this->GetY());
        $this->Ln(10);
    }
    
    /**
     * إضافة معلومات الخطاب
     */
    private function addLetterInfo() {
        $this->SetFont('Arial', '', 11);
        
        // الرقم المرجعي
        $this->Cell(40, 6, iconv('UTF-8', 'windows-1256', 'الرقم المرجعي:'), 0, 0, 'R');
        $this->SetFont('Arial', 'B', 11);
        $this->Cell(0, 6, $this->letterData['reference_number'], 0, 1, 'L');
        
        // التاريخ
        $this->SetFont('Arial', '', 11);
        $this->Cell(40, 6, iconv('UTF-8', 'windows-1256', 'التاريخ:'), 0, 0, 'R');
        $this->SetFont('Arial', 'B', 11);
        $this->Cell(0, 6, $this->letterData['date_hijri'] . ' - ' . $this->letterData['date_gregorian'], 0, 1, 'L');
        
        // رمز التحقق
        $this->SetFont('Arial', '', 11);
        $this->Cell(40, 6, iconv('UTF-8', 'windows-1256', 'رمز التحقق:'), 0, 0, 'R');
        $this->SetFont('Arial', 'B', 11);
        $this->Cell(0, 6, $this->letterData['verification_code'], 0, 1, 'L');
        
        $this->Ln(8);
        
        // المرسل إليه
        $this->SetFont('Arial', 'B', 12);
        $this->Cell(0, 6, iconv('UTF-8', 'windows-1256', 'إلى: ' . $this->letterData['station_name']), 0, 1, 'R');
        $this->SetFont('Arial', '', 11);
        $this->Cell(0, 5, iconv('UTF-8', 'windows-1256', $this->letterData['station_address']), 0, 1, 'R');
        
        $this->Ln(5);
        
        // الموضوع
        $this->SetFont('Arial', 'B', 12);
        $this->Cell(0, 6, iconv('UTF-8', 'windows-1256', 'الموضوع: صرف وقود للمركبة الحكومية'), 0, 1, 'R');
        
        $this->Ln(8);
    }
    
    /**
     * إضافة محتوى الخطاب
     */
    private function addLetterContent() {
        $this->SetFont('Arial', '', 11);
        
        // التحية
        $greeting = 'السلام عليكم ورحمة الله وبركاته،';
        $this->Cell(0, 6, iconv('UTF-8', 'windows-1256', $greeting), 0, 1, 'R');
        $this->Ln(3);
        
        // المحتوى الرئيسي
        $content = sprintf(
            'يرجى التكرم بصرف عدد (%s) لتر من %s للمركبة الحكومية رقم (%s) التابعة لـ%s، ' .
            'وذلك للسائق المفوض (%s) حامل البطاقة الشخصية رقم (%s).',
            $this->letterData['quantity'],
            $this->letterData['fuel_type_arabic'],
            $this->letterData['plate_number'],
            $this->letterData['vehicle_owner'],
            $this->letterData['driver_name'],
            $this->letterData['driver_id']
        );
        
        $this->MultiCell(0, 6, iconv('UTF-8', 'windows-1256', $content), 0, 'R');
        $this->Ln(5);
        
        // تفاصيل إضافية
        $this->SetFont('Arial', 'B', 11);
        $this->Cell(0, 6, iconv('UTF-8', 'windows-1256', 'تفاصيل الطلب:'), 0, 1, 'R');
        
        $this->SetFont('Arial', '', 10);
        $details = [
            'رقم اللوحة: ' . $this->letterData['plate_number'],
            'نوع المركبة: ' . $this->letterData['vehicle_type'],
            'الجهة المالكة: ' . $this->letterData['vehicle_owner'],
            'نوع الوقود: ' . $this->letterData['fuel_type_arabic'],
            'الكمية المطلوبة: ' . $this->letterData['quantity'] . ' لتر',
            'الغرض: ' . $this->letterData['purpose']
        ];
        
        foreach ($details as $detail) {
            $this->Cell(10, 5, iconv('UTF-8', 'windows-1256', '• '), 0, 0, 'R');
            $this->Cell(0, 5, iconv('UTF-8', 'windows-1256', $detail), 0, 1, 'R');
        }
        
        $this->Ln(8);
        
        // ملاحظات مهمة
        $this->SetFont('Arial', 'B', 11);
        $this->SetTextColor(150, 0, 0);
        $this->Cell(0, 6, iconv('UTF-8', 'windows-1256', 'ملاحظات مهمة:'), 0, 1, 'R');
        
        $this->SetFont('Arial', '', 10);
        $this->SetTextColor(0, 0, 0);
        $notes = [
            'هذا الخطاب صالح لمدة 72 ساعة من تاريخ الإصدار',
            'يجب التحقق من صحة الخطاب عبر رمز QR المرفق',
            'لا يجوز استخدام هذا الخطاب أكثر من مرة واحدة',
            'يجب على السائق إبراز البطاقة الشخصية عند الصرف'
        ];
        
        foreach ($notes as $note) {
            $this->Cell(10, 5, iconv('UTF-8', 'windows-1256', '• '), 0, 0, 'R');
            $this->Cell(0, 5, iconv('UTF-8', 'windows-1256', $note), 0, 1, 'R');
        }
        
        $this->Ln(10);
    }
    
    /**
     * إضافة التوقيعات والأختام
     */
    private function addSignaturesAndSeals() {
        $this->SetFont('Arial', '', 11);
        
        // التوقيع
        $this->Cell(0, 6, iconv('UTF-8', 'windows-1256', 'وتفضلوا بقبول فائق الاحترام،'), 0, 1, 'R');
        $this->Ln(15);
        
        // معلومات المدير
        $this->SetFont('Arial', 'B', 11);
        $this->Cell(0, 6, iconv('UTF-8', 'windows-1256', $this->letterData['director_name']), 0, 1, 'R');
        $this->SetFont('Arial', '', 10);
        $this->Cell(0, 5, iconv('UTF-8', 'windows-1256', $this->letterData['director_title']), 0, 1, 'R');
        
        // التوقيع الرقمي
        if ($this->letterData['digital_signature']) {
            $this->Ln(5);
            $this->SetFont('Arial', 'B', 9);
            $this->SetTextColor(0, 100, 0);
            $this->Cell(0, 5, iconv('UTF-8', 'windows-1256', '✓ موقع رقمياً'), 0, 1, 'R');
            $this->SetFont('Arial', '', 8);
            $this->Cell(0, 4, 'Digital Signature: ' . $this->letterData['signature_hash'], 0, 1, 'R');
            $this->SetTextColor(0, 0, 0);
        }
        
        // الختم الرسمي
        $this->Ln(10);
        $this->SetFont('Arial', 'B', 10);
        $this->Cell(0, 5, iconv('UTF-8', 'windows-1256', 'الختم الرسمي'), 0, 1, 'R');
        $this->Rect(150, $this->GetY(), 40, 20);
        $this->SetXY(155, $this->GetY() + 8);
        $this->SetFont('Arial', '', 8);
        $this->Cell(30, 4, iconv('UTF-8', 'windows-1256', 'ختم وزارة المالية'), 0, 1, 'C');
    }
    
    /**
     * إضافة رمز QR للتحقق
     */
    private function addVerificationQR() {
        // إنشاء رمز QR
        $qrHash = $this->sovereignSignature->generateSovereignQR(
            $this->letterData['request_id'], 
            'fuel_letter'
        );
        
        // إضافة معلومات QR
        $this->SetXY(20, $this->GetY() - 15);
        $this->SetFont('Arial', 'B', 9);
        $this->Cell(0, 5, iconv('UTF-8', 'windows-1256', 'للتحقق من صحة الخطاب:'), 0, 1, 'L');
        
        $this->SetFont('Arial', '', 8);
        $verificationUrl = str_replace('{QR_HASH}', $qrHash, $this->config['official_letters']['verification_url']);
        $this->Cell(0, 4, $verificationUrl, 0, 1, 'L');
        
        // رسم مربع QR (سيتم استبداله بمولد QR حقيقي)
        $this->Rect(20, $this->GetY() + 2, 25, 25);
        $this->SetXY(25, $this->GetY() + 12);
        $this->SetFont('Arial', '', 6);
        $this->Cell(15, 3, 'QR CODE', 0, 1, 'C');
        
        $this->letterData['qr_hash'] = $qrHash;
    }
    
    /**
     * إضافة تذييل الخطاب السيادي
     */
    private function addSovereignFooter() {
        $this->SetY(-25);
        $this->SetFont('Arial', '', 8);
        $this->SetTextColor(100, 100, 100);
        
        // معلومات الاتصال
        $contact = 'هاتف: +249-183-123456 | فاكس: +249-183-123457 | البريد الإلكتروني: <EMAIL>';
        $this->Cell(0, 4, $contact, 0, 1, 'C');
        
        // العنوان
        $address = 'العنوان: وزارة المالية والتخطيط الاقتصادي، الخرطوم، جمهورية السودان';
        $this->Cell(0, 4, iconv('UTF-8', 'windows-1256', $address), 0, 1, 'C');
        
        // خط فاصل
        $this->SetDrawColor(200, 200, 200);
        $this->Line(20, $this->GetY() + 2, 190, $this->GetY() + 2);
        
        // حقوق الطبع
        $this->SetY(-10);
        $this->SetFont('Arial', '', 7);
        $copyright = '© 2025 وزارة المالية والتخطيط الاقتصادي - جمهورية السودان | نظام إدارة الوقود الحكومي';
        $this->Cell(0, 4, iconv('UTF-8', 'windows-1256', $copyright), 0, 0, 'C');
    }
    
    /**
     * تحضير بيانات الخطاب
     */
    private function prepareLetterData($requestData) {
        // توليد الرقم المرجعي
        $referenceNumber = $this->generateReferenceNumber();
        
        // التواريخ
        $dateGregorian = date('Y-m-d');
        $dateHijri = $this->convertToHijri($dateGregorian);
        
        // رمز التحقق
        $verificationCode = $this->generateVerificationCode();
        
        return [
            'request_id' => $requestData['id'],
            'reference_number' => $referenceNumber,
            'date_gregorian' => $dateGregorian,
            'date_hijri' => $dateHijri,
            'verification_code' => $verificationCode,
            'station_name' => $requestData['station_name'],
            'station_address' => $requestData['station_location'],
            'plate_number' => $requestData['plate_number'],
            'vehicle_type' => $requestData['vehicle_type'],
            'vehicle_owner' => $requestData['vehicle_owner'],
            'fuel_type_arabic' => $this->getFuelTypeArabic($requestData['fuel_type']),
            'quantity' => $requestData['quantity'],
            'driver_name' => $requestData['driver_name'] ?? 'السائق المفوض',
            'driver_id' => $requestData['driver_id'] ?? 'غير محدد',
            'purpose' => $requestData['purpose'] ?? 'الأغراض الرسمية',
            'director_name' => $this->config['sovereign_info']['director_general'],
            'director_title' => 'المدير العام لإدارة الوقود الحكومي',
            'digital_signature' => true,
            'signature_hash' => hash('sha256', $referenceNumber . time())
        ];
    }
    
    /**
     * توليد الرقم المرجعي
     */
    private function generateReferenceNumber() {
        $format = $this->config['official_letters']['reference_format'];
        $year = date('Y');
        $month = str_pad(date('n'), 2, '0', STR_PAD_LEFT);
        
        // الحصول على الرقم التسلسلي
        $db = Database::getInstance()->getConnection();
        $stmt = $db->prepare("SELECT COUNT(*) + 1 FROM letters WHERE YEAR(created_at) = ? AND MONTH(created_at) = ?");
        $stmt->execute([$year, date('n')]);
        $sequence = str_pad($stmt->fetchColumn(), 6, '0', STR_PAD_LEFT);
        
        return str_replace(['{YEAR}', '{MONTH}', '{SEQUENCE}'], [$year, $month, $sequence], $format);
    }
    
    /**
     * توليد رمز التحقق
     */
    private function generateVerificationCode() {
        return strtoupper(substr(md5(time() . rand()), 0, 8));
    }
    
    /**
     * تحويل التاريخ للهجري (مبسط)
     */
    private function convertToHijri($gregorianDate) {
        // تحويل مبسط - يمكن تحسينه لاحقاً
        $timestamp = strtotime($gregorianDate);
        $hijriYear = date('Y', $timestamp) - 579; // تقريبي
        $hijriMonth = date('n', $timestamp);
        $hijriDay = date('j', $timestamp);
        
        $hijriMonths = [
            1 => 'محرم', 2 => 'صفر', 3 => 'ربيع الأول', 4 => 'ربيع الثاني',
            5 => 'جمادى الأولى', 6 => 'جمادى الثانية', 7 => 'رجب', 8 => 'شعبان',
            9 => 'رمضان', 10 => 'شوال', 11 => 'ذو القعدة', 12 => 'ذو الحجة'
        ];
        
        return $hijriDay . ' ' . $hijriMonths[$hijriMonth] . ' ' . $hijriYear . 'هـ';
    }
    
    /**
     * الحصول على نوع الوقود بالعربية
     */
    private function getFuelTypeArabic($fuelType) {
        $types = [
            'gasoline' => 'البنزين',
            'diesel' => 'الديزل',
            'kerosene' => 'الكيروسين'
        ];
        
        return $types[$fuelType] ?? $fuelType;
    }
    
    /**
     * توليد ملف PDF
     */
    private function generatePDF() {
        $filename = 'fuel_letter_' . $this->letterData['reference_number'] . '.pdf';
        $filepath = 'storage/letters/' . $filename;
        
        // حفظ الملف
        $this->Output('F', $filepath);
        
        // حفظ معلومات الخطاب في قاعدة البيانات
        $this->saveLetterRecord($filepath);
        
        return [
            'filename' => $filename,
            'filepath' => $filepath,
            'reference_number' => $this->letterData['reference_number'],
            'qr_hash' => $this->letterData['qr_hash']
        ];
    }
    
    /**
     * حفظ سجل الخطاب
     */
    private function saveLetterRecord($filepath) {
        $db = Database::getInstance()->getConnection();
        
        $stmt = $db->prepare("
            INSERT INTO letters (
                request_id, letter_number, reference_number, qr_hash, 
                file_path, created_at, expires_at, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $expiresAt = date('Y-m-d H:i:s', strtotime('+72 hours'));
        
        $stmt->execute([
            $this->letterData['request_id'],
            $this->letterData['reference_number'],
            $this->letterData['reference_number'],
            $this->letterData['qr_hash'],
            $filepath,
            date('Y-m-d H:i:s'),
            $expiresAt,
            'active'
        ]);
    }
}
?>
