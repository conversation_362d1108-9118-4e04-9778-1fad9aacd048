<?php

/**
 * نموذج إدارة الحصص السيادية
 * وزارة المالية والتخطيط الاقتصادي - جمهورية السودان
 */

class SovereignQuota {
    private $db;
    private $config;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
        $this->config = require CONFIG_PATH . '/sovereign.php';
    }
    
    /**
     * تخصيص الحصص الشهرية للوزارات
     */
    public function allocateMinistryQuotas($year, $month) {
        $allocations = $this->config['quota_policies']['ministry_allocations'];
        $emergencyReserve = $this->config['quota_policies']['emergency_reserve'];
        
        foreach ($allocations as $ministry => $allocation) {
            $this->createMinistryQuota($ministry, $year, $month, $allocation);
        }
        
        // تخصيص احتياطي الطوارئ
        $this->createEmergencyReserve($year, $month, $emergencyReserve);
        
        // تسجيل في سجل التدقيق
        $this->logQuotaAudit('monthly_allocation', [
            'year' => $year,
            'month' => $month,
            'total_allocated' => array_sum($allocations),
            'emergency_reserve' => $emergencyReserve
        ]);
        
        return true;
    }
    
    /**
     * إنشاء حصة وزارة
     */
    private function createMinistryQuota($ministry, $year, $month, $allocation) {
        $stmt = $this->db->prepare("
            INSERT OR REPLACE INTO ministry_quotas (
                ministry_code, ministry_name, year, month, 
                allocated_amount, consumed_amount, remaining_amount,
                status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, 0, ?, 'active', ?, ?)
        ");
        
        $ministryName = $this->getMinistryName($ministry);
        $now = date('Y-m-d H:i:s');
        
        return $stmt->execute([
            $ministry,
            $ministryName,
            $year,
            $month,
            $allocation,
            $allocation,
            $now,
            $now
        ]);
    }
    
    /**
     * تحديث استهلاك الحصة
     */
    public function updateQuotaConsumption($ministry, $amount, $year = null, $month = null) {
        if (!$year) $year = date('Y');
        if (!$month) $month = date('n');
        
        // التحقق من توفر الحصة
        $quota = $this->getMinistryQuota($ministry, $year, $month);
        if (!$quota) {
            throw new Exception('لا توجد حصة مخصصة لهذه الوزارة');
        }
        
        if ($quota['remaining_amount'] < $amount) {
            // التحقق من إمكانية استخدام احتياطي الطوارئ
            if (!$this->canUseEmergencyReserve($ministry, $amount)) {
                throw new Exception('الكمية المطلوبة تتجاوز الحصة المتاحة');
            }
            
            // استخدام احتياطي الطوارئ
            $this->useEmergencyReserve($amount - $quota['remaining_amount'], $year, $month);
        }
        
        // تحديث الاستهلاك
        $stmt = $this->db->prepare("
            UPDATE ministry_quotas 
            SET consumed_amount = consumed_amount + ?,
                remaining_amount = remaining_amount - ?,
                updated_at = ?
            WHERE ministry_code = ? AND year = ? AND month = ?
        ");
        
        $result = $stmt->execute([
            $amount,
            $amount,
            date('Y-m-d H:i:s'),
            $ministry,
            $year,
            $month
        ]);
        
        // تسجيل الاستهلاك
        $this->logQuotaConsumption($ministry, $amount, $year, $month);
        
        return $result;
    }
    
    /**
     * تصنيف المركبة حسب السياسة السيادية
     */
    public function classifyVehicle($vehicleData) {
        $categories = $this->config['quota_policies']['vehicle_categories'];
        
        // تصنيف تلقائي حسب نوع المركبة والجهة
        if (strpos($vehicleData['owner'], 'وزير') !== false || 
            strpos($vehicleData['owner'], 'وكيل') !== false) {
            return 'executive';
        }
        
        if (strpos($vehicleData['vehicle_type'], 'إسعاف') !== false || 
            strpos($vehicleData['vehicle_type'], 'طوارئ') !== false ||
            strpos($vehicleData['vehicle_type'], 'إطفاء') !== false) {
            return 'emergency';
        }
        
        if (strpos($vehicleData['vehicle_type'], 'حافلة') !== false || 
            strpos($vehicleData['vehicle_type'], 'شاحنة') !== false) {
            return 'transport';
        }
        
        return 'service'; // افتراضي
    }
    
    /**
     * حساب الحصة الشهرية للمركبة
     */
    public function calculateVehicleQuota($vehicleId, $category = null) {
        if (!$category) {
            $vehicle = $this->getVehicleData($vehicleId);
            $category = $this->classifyVehicle($vehicle);
        }
        
        $categoryConfig = $this->config['quota_policies']['vehicle_categories'][$category];
        
        return [
            'category' => $category,
            'monthly_limit' => $categoryConfig['monthly_limit'],
            'priority' => $categoryConfig['priority'],
            'requires_approval' => $categoryConfig['requires_approval']
        ];
    }
    
    /**
     * التحقق من إمكانية الموافقة على الطلب
     */
    public function canApproveRequest($requestData) {
        $vehicle = $this->getVehicleData($requestData['vehicle_id']);
        $ministry = $this->getVehicleMinistry($vehicle);
        $quota = $this->getMinistryQuota($ministry, date('Y'), date('n'));
        
        // التحقق من الحصة الوزارية
        if ($quota['remaining_amount'] < $requestData['quantity']) {
            return [
                'can_approve' => false,
                'reason' => 'تجاوز الحصة الوزارية',
                'requires_ministerial_approval' => true
            ];
        }
        
        // التحقق من حصة المركبة
        $vehicleQuota = $this->getVehicleQuotaStatus($requestData['vehicle_id']);
        if ($vehicleQuota['remaining'] < $requestData['quantity']) {
            return [
                'can_approve' => false,
                'reason' => 'تجاوز حصة المركبة',
                'requires_supervisor_approval' => true
            ];
        }
        
        return ['can_approve' => true];
    }
    
    /**
     * تطبيق السياسة الجغرافية
     */
    public function applyGeographicalPolicy($requestData) {
        $vehicle = $this->getVehicleData($requestData['vehicle_id']);
        $vehicleState = $this->getVehicleState($vehicle);
        
        // الحصول على المحطات المتاحة في الولاية
        $availableStations = $this->getStateStations($vehicleState);
        
        // ترشيح أقرب محطة
        $recommendedStation = $this->findNearestStation($vehicle, $availableStations);
        
        return [
            'vehicle_state' => $vehicleState,
            'available_stations' => $availableStations,
            'recommended_station' => $recommendedStation
        ];
    }
    
    /**
     * إنشاء تقرير الاستهلاك الوزاري
     */
    public function generateMinistryConsumptionReport($ministry, $year, $month) {
        $stmt = $this->db->prepare("
            SELECT 
                mq.*,
                COUNT(fr.id) as total_requests,
                SUM(CASE WHEN fr.status = 'approved' THEN fr.quantity ELSE 0 END) as approved_consumption,
                COUNT(CASE WHEN fr.status = 'pending' THEN 1 END) as pending_requests,
                COUNT(CASE WHEN fr.status = 'rejected' THEN 1 END) as rejected_requests
            FROM ministry_quotas mq
            LEFT JOIN fuel_requests fr ON fr.ministry_code = mq.ministry_code 
                AND YEAR(fr.request_date) = mq.year 
                AND MONTH(fr.request_date) = mq.month
            WHERE mq.ministry_code = ? AND mq.year = ? AND mq.month = ?
            GROUP BY mq.id
        ");
        
        $stmt->execute([$ministry, $year, $month]);
        $report = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($report) {
            $report['efficiency_rate'] = ($report['allocated_amount'] > 0) ? 
                ($report['consumed_amount'] / $report['allocated_amount']) * 100 : 0;
            $report['utilization_rate'] = ($report['allocated_amount'] > 0) ? 
                ($report['approved_consumption'] / $report['allocated_amount']) * 100 : 0;
        }
        
        return $report;
    }
    
    /**
     * تحليل الاتجاهات الاستهلاكية
     */
    public function analyzeConsumptionTrends($ministry, $months = 6) {
        $stmt = $this->db->prepare("
            SELECT 
                year, month, allocated_amount, consumed_amount,
                (consumed_amount * 100.0 / allocated_amount) as utilization_percentage
            FROM ministry_quotas 
            WHERE ministry_code = ? 
            ORDER BY year DESC, month DESC 
            LIMIT ?
        ");
        
        $stmt->execute([$ministry, $months]);
        $trends = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // حساب المتوسطات والاتجاهات
        $analysis = [
            'average_utilization' => 0,
            'trend_direction' => 'stable',
            'peak_month' => null,
            'low_month' => null,
            'data' => $trends
        ];
        
        if (!empty($trends)) {
            $utilizations = array_column($trends, 'utilization_percentage');
            $analysis['average_utilization'] = array_sum($utilizations) / count($utilizations);
            
            // تحديد الاتجاه
            if (count($trends) >= 3) {
                $recent = array_slice($utilizations, 0, 3);
                $older = array_slice($utilizations, -3);
                
                if (array_sum($recent) > array_sum($older)) {
                    $analysis['trend_direction'] = 'increasing';
                } elseif (array_sum($recent) < array_sum($older)) {
                    $analysis['trend_direction'] = 'decreasing';
                }
            }
            
            // تحديد الذروة والقاع
            $maxIndex = array_search(max($utilizations), $utilizations);
            $minIndex = array_search(min($utilizations), $utilizations);
            
            $analysis['peak_month'] = $trends[$maxIndex];
            $analysis['low_month'] = $trends[$minIndex];
        }
        
        return $analysis;
    }
    
    // دوال مساعدة
    private function getMinistryName($code) {
        $names = [
            'finance' => 'وزارة المالية والتخطيط الاقتصادي',
            'interior' => 'وزارة الداخلية',
            'defense' => 'وزارة الدفاع',
            'health' => 'وزارة الصحة',
            'education' => 'وزارة التعليم',
            'transport' => 'وزارة النقل',
            'energy' => 'وزارة الطاقة والتعدين',
            'agriculture' => 'وزارة الزراعة والغابات',
            'water' => 'وزارة الموارد المائية والري',
            'telecommunications' => 'وزارة الاتصالات وتقنية المعلومات'
        ];
        
        return $names[$code] ?? $code;
    }
    
    private function getMinistryQuota($ministry, $year, $month) {
        $stmt = $this->db->prepare("
            SELECT * FROM ministry_quotas 
            WHERE ministry_code = ? AND year = ? AND month = ?
        ");
        $stmt->execute([$ministry, $year, $month]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function logQuotaAudit($action, $details) {
        $stmt = $this->db->prepare("
            INSERT INTO quota_audit_log (
                action, details, user_id, timestamp
            ) VALUES (?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $action,
            json_encode($details),
            $_SESSION['user_id'] ?? null,
            date('Y-m-d H:i:s')
        ]);
    }
    
    private function logQuotaConsumption($ministry, $amount, $year, $month) {
        $stmt = $this->db->prepare("
            INSERT INTO quota_consumption_log (
                ministry_code, amount, year, month, timestamp, user_id
            ) VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $ministry,
            $amount,
            $year,
            $month,
            date('Y-m-d H:i:s'),
            $_SESSION['user_id'] ?? null
        ]);
    }
}
?>
