<?php

/**
 * نموذج التوقيع الرقمي السيادي
 * وزارة المالية والتخطيط الاقتصادي - جمهورية السودان
 */

class SovereignSignature {
    private $db;
    private $config;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
        $this->config = require CONFIG_PATH . '/sovereign.php';
    }
    
    /**
     * إنشاء توقيع رقمي سيادي
     */
    public function createSignature($documentId, $signerData, $signatureLevel) {
        $signatureData = [
            'document_id' => $documentId,
            'signer_id' => $signerData['user_id'],
            'signer_name' => $signerData['name'],
            'signer_title' => $signerData['title'],
            'signature_level' => $signatureLevel,
            'signature_hash' => $this->generateSignatureHash($documentId, $signerData),
            'certificate_serial' => $this->generateCertificateSerial(),
            'timestamp' => date('Y-m-d H:i:s'),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'validity_start' => date('Y-m-d H:i:s'),
            'validity_end' => date('Y-m-d H:i:s', strtotime('+72 hours')),
            'status' => 'active'
        ];
        
        $stmt = $this->db->prepare("
            INSERT INTO sovereign_signatures (
                document_id, signer_id, signer_name, signer_title, signature_level,
                signature_hash, certificate_serial, timestamp, ip_address, user_agent,
                validity_start, validity_end, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            $signatureData['document_id'],
            $signatureData['signer_id'],
            $signatureData['signer_name'],
            $signatureData['signer_title'],
            $signatureData['signature_level'],
            $signatureData['signature_hash'],
            $signatureData['certificate_serial'],
            $signatureData['timestamp'],
            $signatureData['ip_address'],
            $signatureData['user_agent'],
            $signatureData['validity_start'],
            $signatureData['validity_end'],
            $signatureData['status']
        ]);
        
        if ($result) {
            $signatureId = $this->db->lastInsertId();
            
            // تسجيل في سجل التدقيق السيادي
            $this->logSovereignAudit('signature_created', [
                'signature_id' => $signatureId,
                'document_id' => $documentId,
                'signer_id' => $signerData['user_id'],
                'level' => $signatureLevel
            ]);
            
            return $signatureId;
        }
        
        return false;
    }
    
    /**
     * التحقق من صحة التوقيع الرقمي
     */
    public function verifySignature($signatureHash) {
        $stmt = $this->db->prepare("
            SELECT * FROM sovereign_signatures 
            WHERE signature_hash = ? AND status = 'active' 
            AND validity_end > CURRENT_TIMESTAMP
        ");
        $stmt->execute([$signatureHash]);
        $signature = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($signature) {
            // تسجيل عملية التحقق
            $this->logSovereignAudit('signature_verified', [
                'signature_id' => $signature['id'],
                'verification_ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
            
            return [
                'valid' => true,
                'signature' => $signature,
                'verification_time' => date('Y-m-d H:i:s')
            ];
        }
        
        return ['valid' => false, 'reason' => 'توقيع غير صحيح أو منتهي الصلاحية'];
    }
    
    /**
     * إنشاء ختم رقمي سيادي
     */
    public function createSovereignSeal($documentId, $sealType = 'ministry') {
        $sealData = [
            'document_id' => $documentId,
            'seal_type' => $sealType,
            'seal_authority' => $this->config['sovereign_info']['ministry'],
            'seal_hash' => $this->generateSealHash($documentId, $sealType),
            'created_at' => date('Y-m-d H:i:s'),
            'legal_authority' => $this->config['sovereign_info']['legal_authority'],
            'status' => 'active'
        ];
        
        $stmt = $this->db->prepare("
            INSERT INTO sovereign_seals (
                document_id, seal_type, seal_authority, seal_hash, 
                created_at, legal_authority, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            $sealData['document_id'],
            $sealData['seal_type'],
            $sealData['seal_authority'],
            $sealData['seal_hash'],
            $sealData['created_at'],
            $sealData['legal_authority'],
            $sealData['status']
        ]);
        
        if ($result) {
            return $this->db->lastInsertId();
        }
        
        return false;
    }
    
    /**
     * توليد رمز QR للتحقق السيادي
     */
    public function generateSovereignQR($documentId, $documentType = 'fuel_letter') {
        $qrData = [
            'document_id' => $documentId,
            'document_type' => $documentType,
            'issuing_authority' => $this->config['sovereign_info']['ministry'],
            'country' => $this->config['sovereign_info']['country'],
            'verification_url' => $this->config['official_letters']['verification_url'],
            'timestamp' => time(),
            'hash' => $this->generateQRHash($documentId, $documentType)
        ];
        
        // تشفير البيانات
        $encryptedData = $this->encryptSovereignData(json_encode($qrData));
        
        // حفظ في قاعدة البيانات
        $stmt = $this->db->prepare("
            INSERT INTO sovereign_qr_codes (
                document_id, document_type, qr_hash, encrypted_data, 
                created_at, expires_at, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $expiresAt = date('Y-m-d H:i:s', strtotime('+72 hours'));
        
        $stmt->execute([
            $documentId,
            $documentType,
            $qrData['hash'],
            $encryptedData,
            date('Y-m-d H:i:s'),
            $expiresAt,
            'active'
        ]);
        
        return $qrData['hash'];
    }
    
    /**
     * التحقق من رمز QR السيادي
     */
    public function verifySovereignQR($qrHash) {
        $stmt = $this->db->prepare("
            SELECT * FROM sovereign_qr_codes 
            WHERE qr_hash = ? AND status = 'active' 
            AND expires_at > CURRENT_TIMESTAMP
        ");
        $stmt->execute([$qrHash]);
        $qrRecord = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($qrRecord) {
            // فك تشفير البيانات
            $decryptedData = $this->decryptSovereignData($qrRecord['encrypted_data']);
            $qrData = json_decode($decryptedData, true);
            
            // تسجيل عملية التحقق
            $this->logSovereignAudit('qr_verified', [
                'qr_id' => $qrRecord['id'],
                'document_id' => $qrRecord['document_id'],
                'verification_ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
            
            return [
                'valid' => true,
                'data' => $qrData,
                'document_info' => $qrRecord
            ];
        }
        
        return ['valid' => false, 'reason' => 'رمز QR غير صحيح أو منتهي الصلاحية'];
    }
    
    /**
     * تسجيل في سجل التدقيق السيادي
     */
    private function logSovereignAudit($action, $details) {
        $stmt = $this->db->prepare("
            INSERT INTO sovereign_audit_log (
                action, details, user_id, ip_address, user_agent, timestamp
            ) VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $action,
            json_encode($details),
            $_SESSION['user_id'] ?? null,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * توليد هاش التوقيع
     */
    private function generateSignatureHash($documentId, $signerData) {
        $data = $documentId . $signerData['user_id'] . $signerData['name'] . time();
        return hash('sha256', $data);
    }
    
    /**
     * توليد رقم الشهادة
     */
    private function generateCertificateSerial() {
        return 'CERT-SD-' . date('Y') . '-' . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT);
    }
    
    /**
     * توليد هاش الختم
     */
    private function generateSealHash($documentId, $sealType) {
        $data = $documentId . $sealType . $this->config['sovereign_info']['ministry'] . time();
        return hash('sha256', $data);
    }
    
    /**
     * توليد هاش QR
     */
    private function generateQRHash($documentId, $documentType) {
        $data = $documentId . $documentType . time() . rand(1000, 9999);
        return hash('sha256', $data);
    }
    
    /**
     * تشفير البيانات السيادية
     */
    private function encryptSovereignData($data) {
        $key = hash('sha256', 'SUDAN_SOVEREIGN_KEY_2025', true);
        $iv = openssl_random_pseudo_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * فك تشفير البيانات السيادية
     */
    private function decryptSovereignData($encryptedData) {
        $key = hash('sha256', 'SUDAN_SOVEREIGN_KEY_2025', true);
        $data = base64_decode($encryptedData);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
    }
    
    /**
     * الحصول على إحصائيات التوقيع السيادي
     */
    public function getSovereignStatistics($period = 'monthly') {
        $dateCondition = '';
        switch ($period) {
            case 'daily':
                $dateCondition = "WHERE DATE(timestamp) = DATE('now')";
                break;
            case 'weekly':
                $dateCondition = "WHERE DATE(timestamp) >= DATE('now', '-7 days')";
                break;
            case 'monthly':
                $dateCondition = "WHERE DATE(timestamp) >= DATE('now', 'start of month')";
                break;
            case 'yearly':
                $dateCondition = "WHERE DATE(timestamp) >= DATE('now', 'start of year')";
                break;
        }
        
        $stmt = $this->db->prepare("
            SELECT 
                COUNT(*) as total_signatures,
                COUNT(CASE WHEN signature_level = 'minister' THEN 1 END) as ministerial_signatures,
                COUNT(CASE WHEN signature_level = 'director' THEN 1 END) as directorial_signatures,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_signatures,
                COUNT(CASE WHEN validity_end < CURRENT_TIMESTAMP THEN 1 END) as expired_signatures
            FROM sovereign_signatures 
            $dateCondition
        ");
        
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
?>
