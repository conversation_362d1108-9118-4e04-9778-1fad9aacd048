<?php

class Station {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    public function create($data) {
        $stmt = $this->db->prepare("
            INSERT INTO stations (name, code, location, latitude, longitude, managed_by) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        return $stmt->execute([
            $data['name'],
            $data['code'],
            $data['location'],
            $data['latitude'] ?? null,
            $data['longitude'] ?? null,
            $data['managed_by']
        ]);
    }
    
    public function findById($id) {
        $stmt = $this->db->prepare("SELECT * FROM stations WHERE id = ? AND is_active = 1");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function findByCode($code) {
        $stmt = $this->db->prepare("SELECT * FROM stations WHERE code = ? AND is_active = 1");
        $stmt->execute([$code]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function getAll() {
        $stmt = $this->db->prepare("
            SELECT * FROM stations 
            WHERE is_active = 1 
            ORDER BY name
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getForSelect() {
        $stmt = $this->db->prepare("
            SELECT id, name, code, location 
            FROM stations 
            WHERE is_active = 1 
            ORDER BY name
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function update($id, $data) {
        $fields = [];
        $values = [];
        
        foreach ($data as $key => $value) {
            if ($key !== 'id') {
                $fields[] = "$key = ?";
                $values[] = $value;
            }
        }
        
        if (empty($fields)) return false;
        
        $values[] = $id;
        $sql = "UPDATE stations SET " . implode(', ', $fields) . " WHERE id = ?";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($values);
    }
    
    public function delete($id) {
        $stmt = $this->db->prepare("UPDATE stations SET is_active = 0 WHERE id = ?");
        return $stmt->execute([$id]);
    }
    
    public function getStationRequests($stationId, $status = null, $limit = null) {
        $whereClause = "WHERE fr.station_id = ?";
        $params = [$stationId];
        
        if ($status) {
            $whereClause .= " AND fr.status = ?";
            $params[] = $status;
        }
        
        $limitClause = $limit ? "LIMIT $limit" : "";
        
        $stmt = $this->db->prepare("
            SELECT 
                fr.*,
                v.plate_number,
                v.owner,
                v.vehicle_type,
                u.name as requester_name
            FROM fuel_requests fr
            JOIN vehicles v ON fr.vehicle_id = v.id
            JOIN users u ON fr.user_id = u.id
            $whereClause
            ORDER BY fr.request_date DESC
            $limitClause
        ");
        
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getStationStatistics($stationId, $period = 'monthly') {
        $dateCondition = '';
        switch ($period) {
            case 'daily':
                $dateCondition = "AND DATE(fr.request_date) = DATE('now')";
                break;
            case 'weekly':
                $dateCondition = "AND DATE(fr.request_date) >= DATE('now', '-7 days')";
                break;
            case 'monthly':
                $dateCondition = "AND DATE(fr.request_date) >= DATE('now', 'start of month')";
                break;
            case 'yearly':
                $dateCondition = "AND DATE(fr.request_date) >= DATE('now', 'start of year')";
                break;
        }
        
        $stmt = $this->db->prepare("
            SELECT 
                COUNT(*) as total_requests,
                COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_requests,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_requests,
                COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_requests,
                SUM(CASE WHEN status = 'approved' THEN quantity ELSE 0 END) as total_fuel_dispensed,
                SUM(CASE WHEN status = 'approved' AND fuel_type = 'gasoline' THEN quantity ELSE 0 END) as gasoline_dispensed,
                SUM(CASE WHEN status = 'approved' AND fuel_type = 'diesel' THEN quantity ELSE 0 END) as diesel_dispensed
            FROM fuel_requests fr
            WHERE fr.station_id = ? $dateCondition
        ");
        
        $stmt->execute([$stationId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function getNearbyStations($latitude, $longitude, $radius = 10) {
        // حساب المحطات القريبة باستخدام معادلة Haversine
        $stmt = $this->db->prepare("
            SELECT *,
                (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * 
                cos(radians(longitude) - radians(?)) + sin(radians(?)) * 
                sin(radians(latitude)))) AS distance
            FROM stations
            WHERE is_active = 1 
            AND latitude IS NOT NULL 
            AND longitude IS NOT NULL
            HAVING distance < ?
            ORDER BY distance
        ");
        
        $stmt->execute([$latitude, $longitude, $latitude, $radius]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getMonthlyFuelDistribution($stationId) {
        $stmt = $this->db->prepare("
            SELECT 
                strftime('%Y-%m', request_date) as month,
                fuel_type,
                SUM(quantity) as total_quantity,
                COUNT(*) as request_count
            FROM fuel_requests 
            WHERE station_id = ? 
            AND status = 'approved'
            AND request_date >= DATE('now', '-12 months')
            GROUP BY strftime('%Y-%m', request_date), fuel_type
            ORDER BY month DESC, fuel_type
        ");
        
        $stmt->execute([$stationId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function validateStationCapacity($stationId, $fuelType, $requestedQuantity) {
        // هذه الوظيفة يمكن توسيعها لاحقاً للتحقق من سعة المحطة
        // حالياً ترجع true دائماً
        return true;
    }
    
    public function getTopRequestingEntities($stationId, $limit = 10) {
        $stmt = $this->db->prepare("
            SELECT 
                v.owner,
                COUNT(fr.id) as request_count,
                SUM(fr.quantity) as total_fuel,
                AVG(fr.quantity) as avg_per_request
            FROM fuel_requests fr
            JOIN vehicles v ON fr.vehicle_id = v.id
            WHERE fr.station_id = ? 
            AND fr.status = 'approved'
            AND fr.request_date >= DATE('now', '-3 months')
            GROUP BY v.owner
            ORDER BY total_fuel DESC
            LIMIT ?
        ");
        
        $stmt->execute([$stationId, $limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
