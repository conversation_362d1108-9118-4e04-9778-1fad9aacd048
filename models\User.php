<?php

class User {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    public function authenticate($email, $password) {
        $stmt = $this->db->prepare("
            SELECT id, name, email, password, role, station_id, is_active 
            FROM users 
            WHERE email = ? AND is_active = 1
        ");
        $stmt->execute([$email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user && password_verify($password, $user['password'])) {
            // تحديث آخر تسجيل دخول
            $this->updateLastLogin($user['id']);
            return $user;
        }
        
        return false;
    }
    
    public function create($data) {
        $stmt = $this->db->prepare("
            INSERT INTO users (name, email, password, role, station_id) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
        
        return $stmt->execute([
            $data['name'],
            $data['email'],
            $hashedPassword,
            $data['role'],
            $data['station_id'] ?? null
        ]);
    }
    
    public function findById($id) {
        $stmt = $this->db->prepare("
            SELECT u.*, s.name as station_name 
            FROM users u 
            LEFT JOIN stations s ON u.station_id = s.id 
            WHERE u.id = ?
        ");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function getAll() {
        $stmt = $this->db->prepare("
            SELECT u.*, s.name as station_name 
            FROM users u 
            LEFT JOIN stations s ON u.station_id = s.id 
            ORDER BY u.created_at DESC
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function update($id, $data) {
        $fields = [];
        $values = [];
        
        foreach ($data as $key => $value) {
            if ($key !== 'id') {
                if ($key === 'password' && !empty($value)) {
                    $fields[] = "$key = ?";
                    $values[] = password_hash($value, PASSWORD_DEFAULT);
                } elseif ($key !== 'password') {
                    $fields[] = "$key = ?";
                    $values[] = $value;
                }
            }
        }
        
        if (empty($fields)) return false;
        
        $values[] = $id;
        $sql = "UPDATE users SET " . implode(', ', $fields) . " WHERE id = ?";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($values);
    }
    
    public function delete($id) {
        $stmt = $this->db->prepare("UPDATE users SET is_active = 0 WHERE id = ?");
        return $stmt->execute([$id]);
    }
    
    public function hasPermission($userId, $permission) {
        $user = $this->findById($userId);
        if (!$user) return false;
        
        $permissions = [
            'admin' => ['all'],
            'supervisor' => ['view_reports', 'manage_requests', 'manage_vehicles'],
            'requester' => ['create_request', 'view_own_requests'],
            'station' => ['view_station_requests', 'confirm_fuel_delivery'],
            'reviewer' => ['review_requests', 'generate_reports']
        ];
        
        $userPermissions = $permissions[$user['role']] ?? [];
        
        return in_array('all', $userPermissions) || in_array($permission, $userPermissions);
    }
    
    private function updateLastLogin($userId) {
        $stmt = $this->db->prepare("UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?");
        $stmt->execute([$userId]);
    }
    
    public function logActivity($userId, $action, $tableName = null, $recordId = null, $oldValues = null, $newValues = null) {
        $stmt = $this->db->prepare("
            INSERT INTO activity_logs (user_id, action, table_name, record_id, old_values, new_values, ip_address) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        return $stmt->execute([
            $userId,
            $action,
            $tableName,
            $recordId,
            $oldValues ? json_encode($oldValues) : null,
            $newValues ? json_encode($newValues) : null,
            $_SERVER['REMOTE_ADDR'] ?? null
        ]);
    }
}
?>
