<?php

class Vehicle {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    public function create($data) {
        $stmt = $this->db->prepare("
            INSERT INTO vehicles (plate_number, chasis_number, owner, vehicle_type, fuel_type, tank_capacity) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        return $stmt->execute([
            $data['plate_number'],
            $data['chasis_number'],
            $data['owner'],
            $data['vehicle_type'],
            $data['fuel_type'],
            $data['tank_capacity']
        ]);
    }
    
    public function findById($id) {
        $stmt = $this->db->prepare("SELECT * FROM vehicles WHERE id = ? AND is_active = 1");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function findByPlateNumber($plateNumber) {
        $stmt = $this->db->prepare("SELECT * FROM vehicles WHERE plate_number = ? AND is_active = 1");
        $stmt->execute([$plateNumber]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function search($query) {
        $stmt = $this->db->prepare("
            SELECT * FROM vehicles 
            WHERE (plate_number LIKE ? OR chasis_number LIKE ? OR owner LIKE ?) 
            AND is_active = 1 
            ORDER BY plate_number
        ");
        $searchTerm = "%$query%";
        $stmt->execute([$searchTerm, $searchTerm, $searchTerm]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getAll($limit = null, $offset = 0) {
        $sql = "SELECT * FROM vehicles WHERE is_active = 1 ORDER BY created_at DESC";
        if ($limit) {
            $sql .= " LIMIT $limit OFFSET $offset";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getByOwner($owner) {
        $stmt = $this->db->prepare("
            SELECT * FROM vehicles 
            WHERE owner = ? AND is_active = 1 
            ORDER BY plate_number
        ");
        $stmt->execute([$owner]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function update($id, $data) {
        $fields = [];
        $values = [];
        
        foreach ($data as $key => $value) {
            if ($key !== 'id') {
                $fields[] = "$key = ?";
                $values[] = $value;
            }
        }
        
        if (empty($fields)) return false;
        
        $values[] = $id;
        $sql = "UPDATE vehicles SET " . implode(', ', $fields) . " WHERE id = ?";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($values);
    }
    
    public function delete($id) {
        $stmt = $this->db->prepare("UPDATE vehicles SET is_active = 0 WHERE id = ?");
        return $stmt->execute([$id]);
    }
    
    public function getFuelConsumption($vehicleId, $period = 'monthly') {
        $dateCondition = '';
        switch ($period) {
            case 'daily':
                $dateCondition = "AND DATE(fr.request_date) = DATE('now')";
                break;
            case 'weekly':
                $dateCondition = "AND DATE(fr.request_date) >= DATE('now', '-7 days')";
                break;
            case 'monthly':
                $dateCondition = "AND DATE(fr.request_date) >= DATE('now', 'start of month')";
                break;
            case 'yearly':
                $dateCondition = "AND DATE(fr.request_date) >= DATE('now', 'start of year')";
                break;
        }
        
        $stmt = $this->db->prepare("
            SELECT 
                SUM(fr.quantity) as total_consumption,
                COUNT(fr.id) as total_requests,
                AVG(fr.quantity) as avg_per_request
            FROM fuel_requests fr 
            WHERE fr.vehicle_id = ? AND fr.status = 'approved' $dateCondition
        ");
        $stmt->execute([$vehicleId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function getQuotaStatus($vehicleId) {
        $vehicle = $this->findById($vehicleId);
        if (!$vehicle) return null;
        
        // حساب الاستهلاك الشهري
        $consumption = $this->getFuelConsumption($vehicleId, 'monthly');
        
        // تحديد نوع الجهة وحدود الحصة
        $ownerType = $this->determineOwnerType($vehicle['owner']);
        $config = require CONFIG_PATH . '/app.php';
        $monthlyLimit = $config['quota_limits'][$ownerType] ?? 500;
        
        return [
            'monthly_limit' => $monthlyLimit,
            'consumed' => $consumption['total_consumption'] ?? 0,
            'remaining' => $monthlyLimit - ($consumption['total_consumption'] ?? 0),
            'percentage_used' => (($consumption['total_consumption'] ?? 0) / $monthlyLimit) * 100
        ];
    }
    
    private function determineOwnerType($owner) {
        $militaryKeywords = ['جيش', 'قوات', 'شرطة', 'أمن', 'دفاع'];
        $serviceKeywords = ['صحة', 'تعليم', 'مياه', 'كهرباء', 'اتصالات'];
        
        $owner = strtolower($owner);
        
        foreach ($militaryKeywords as $keyword) {
            if (strpos($owner, $keyword) !== false) {
                return 'military';
            }
        }
        
        foreach ($serviceKeywords as $keyword) {
            if (strpos($owner, $keyword) !== false) {
                return 'service';
            }
        }
        
        return 'civilian';
    }
    
    public function importFromExcel($filePath) {
        // سيتم تنفيذ هذه الوظيفة لاحقاً لاستيراد البيانات من Excel
        return false;
    }
    
    public function getStatistics() {
        $stmt = $this->db->prepare("
            SELECT 
                COUNT(*) as total_vehicles,
                COUNT(CASE WHEN vehicle_type = 'سيارة' THEN 1 END) as cars,
                COUNT(CASE WHEN vehicle_type = 'شاحنة' THEN 1 END) as trucks,
                COUNT(CASE WHEN vehicle_type = 'حافلة' THEN 1 END) as buses,
                COUNT(CASE WHEN fuel_type = 'gasoline' THEN 1 END) as gasoline_vehicles,
                COUNT(CASE WHEN fuel_type = 'diesel' THEN 1 END) as diesel_vehicles
            FROM vehicles 
            WHERE is_active = 1
        ");
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
?>
