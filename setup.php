<?php
/**
 * ملف إعداد وتهيئة نظام صرف حصص الوقود الذكي
 * يقوم بإنشاء قاعدة البيانات وإدراج البيانات التجريبية
 */

// تحديد المسارات
define('ROOT_PATH', __DIR__);
define('CONFIG_PATH', ROOT_PATH . '/config');

// تحميل إعدادات قاعدة البيانات
require_once CONFIG_PATH . '/database.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعداد نظام صرف حصص الوقود الذكي</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .setup-card { background: rgba(255, 255, 255, 0.95); border-radius: 20px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1); }
        .step { padding: 15px; margin: 10px 0; border-radius: 10px; }
        .step.success { background: #d4edda; border: 1px solid #c3e6cb; }
        .step.error { background: #f8d7da; border: 1px solid #f5c6cb; }
        .step.info { background: #d1ecf1; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class='container mt-5'>
        <div class='row justify-content-center'>
            <div class='col-md-8'>
                <div class='setup-card p-4'>
                    <div class='text-center mb-4'>
                        <i class='fas fa-gas-pump fa-4x text-primary mb-3'></i>
                        <h2 class='text-primary'>إعداد نظام صرف حصص الوقود الذكي</h2>
                        <p class='text-muted'>وزارة المالية والتخطيط الاقتصادي - ولاية الخرطوم</p>
                    </div>";

try {
    // الخطوة 1: إنشاء مجلدات التخزين
    echo "<div class='step info'>
            <h5><i class='fas fa-folder me-2'></i>الخطوة 1: إنشاء مجلدات التخزين</h5>";
    
    $directories = [
        'database',
        'storage',
        'storage/letters',
        'storage/reports',
        'storage/uploads'
    ];
    
    foreach ($directories as $dir) {
        $path = ROOT_PATH . '/' . $dir;
        if (!file_exists($path)) {
            if (mkdir($path, 0755, true)) {
                echo "<p class='text-success mb-1'><i class='fas fa-check me-2'></i>تم إنشاء مجلد: $dir</p>";
            } else {
                echo "<p class='text-danger mb-1'><i class='fas fa-times me-2'></i>فشل في إنشاء مجلد: $dir</p>";
            }
        } else {
            echo "<p class='text-info mb-1'><i class='fas fa-info-circle me-2'></i>المجلد موجود مسبقاً: $dir</p>";
        }
    }
    echo "</div>";

    // الخطوة 2: إنشاء قاعدة البيانات
    echo "<div class='step info'>
            <h5><i class='fas fa-database me-2'></i>الخطوة 2: إنشاء قاعدة البيانات</h5>";
    
    $db = Database::getInstance();
    echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم إنشاء قاعدة البيانات بنجاح</p>";
    echo "</div>";

    // الخطوة 3: إدراج بيانات تجريبية إضافية
    echo "<div class='step info'>
            <h5><i class='fas fa-plus me-2'></i>الخطوة 3: إدراج البيانات التجريبية</h5>";
    
    $connection = $db->getConnection();
    
    // إضافة مستخدمين تجريبيين
    $users = [
        ['أحمد محمد علي', '<EMAIL>', 'supervisor123', 'supervisor', null],
        ['فاطمة أحمد محمد', '<EMAIL>', 'requester123', 'requester', null],
        ['محمد عبدالله حسن', '<EMAIL>', 'station123', 'station', 1],
        ['سارة علي محمد', '<EMAIL>', 'reviewer123', 'reviewer', null]
    ];
    
    foreach ($users as $user) {
        $hashedPassword = password_hash($user[2], PASSWORD_DEFAULT);
        $stmt = $connection->prepare("
            INSERT OR IGNORE INTO users (name, email, password, role, station_id) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$user[0], $user[1], $hashedPassword, $user[3], $user[4]]);
    }
    echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم إدراج المستخدمين التجريبيين</p>";
    
    // إضافة مركبات تجريبية
    $vehicles = [
        ['س و 1234', 'ABC123456789', 'وزارة الصحة', 'سيارة', 'gasoline', 60],
        ['س و 5678', 'DEF987654321', 'وزارة التعليم', 'حافلة', 'diesel', 200],
        ['س و 9012', 'GHI456789123', 'وزارة الداخلية', 'شاحنة', 'diesel', 300],
        ['س و 3456', 'JKL789123456', 'وزارة الدفاع', 'سيارة', 'gasoline', 80],
        ['س و 7890', 'MNO123456789', 'وزارة المياه', 'شاحنة', 'diesel', 250]
    ];
    
    foreach ($vehicles as $vehicle) {
        $stmt = $connection->prepare("
            INSERT OR IGNORE INTO vehicles (plate_number, chasis_number, owner, vehicle_type, fuel_type, tank_capacity) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute($vehicle);
    }
    echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم إدراج المركبات التجريبية</p>";
    
    // إضافة طلبات تجريبية
    $requests = [
        [1, 1, 3, 'gasoline', 50, 'approved', 'طلب عادي للدوريات'],
        [2, 2, 3, 'diesel', 150, 'pending', 'نقل الطلاب'],
        [3, 3, 3, 'diesel', 200, 'approved', 'نقل المعدات'],
        [4, 4, 3, 'gasoline', 60, 'pending', 'مهام أمنية'],
        [5, 5, 3, 'diesel', 180, 'rejected', 'تجاوز الحصة المسموحة']
    ];
    
    foreach ($requests as $request) {
        $stmt = $connection->prepare("
            INSERT OR IGNORE INTO fuel_requests (vehicle_id, station_id, user_id, fuel_type, quantity, status, notes) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute($request);
    }
    echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم إدراج طلبات الوقود التجريبية</p>";
    
    // إضافة خطابات تجريبية
    $letters = [
        [1, 'FUEL-20250123-001', 'hash1234567890abcdef', null, 0],
        [3, 'FUEL-20250123-002', 'hash0987654321fedcba', null, 0]
    ];
    
    foreach ($letters as $letter) {
        $stmt = $connection->prepare("
            INSERT OR IGNORE INTO letters (request_id, letter_number, qr_hash, printed_at, is_used) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute($letter);
    }
    echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم إدراج الخطابات التجريبية</p>";
    
    echo "</div>";

    // الخطوة 4: التحقق من النظام
    echo "<div class='step success'>
            <h5><i class='fas fa-check-circle me-2'></i>الخطوة 4: التحقق من النظام</h5>";
    
    // عدد المستخدمين
    $stmt = $connection->query("SELECT COUNT(*) FROM users");
    $userCount = $stmt->fetchColumn();
    echo "<p class='mb-1'><strong>عدد المستخدمين:</strong> $userCount</p>";
    
    // عدد المركبات
    $stmt = $connection->query("SELECT COUNT(*) FROM vehicles");
    $vehicleCount = $stmt->fetchColumn();
    echo "<p class='mb-1'><strong>عدد المركبات:</strong> $vehicleCount</p>";
    
    // عدد المحطات
    $stmt = $connection->query("SELECT COUNT(*) FROM stations");
    $stationCount = $stmt->fetchColumn();
    echo "<p class='mb-1'><strong>عدد المحطات:</strong> $stationCount</p>";
    
    // عدد الطلبات
    $stmt = $connection->query("SELECT COUNT(*) FROM fuel_requests");
    $requestCount = $stmt->fetchColumn();
    echo "<p class='mb-1'><strong>عدد طلبات الوقود:</strong> $requestCount</p>";
    
    echo "</div>";

    // معلومات تسجيل الدخول
    echo "<div class='step success'>
            <h5><i class='fas fa-key me-2'></i>بيانات تسجيل الدخول</h5>
            <div class='row'>
                <div class='col-md-6'>
                    <h6 class='text-primary'>مدير النظام</h6>
                    <p class='mb-1'><strong>البريد:</strong> <EMAIL></p>
                    <p class='mb-3'><strong>كلمة المرور:</strong> admin123</p>
                    
                    <h6 class='text-primary'>مشرف</h6>
                    <p class='mb-1'><strong>البريد:</strong> <EMAIL></p>
                    <p class='mb-3'><strong>كلمة المرور:</strong> supervisor123</p>
                </div>
                <div class='col-md-6'>
                    <h6 class='text-primary'>جهة طلب</h6>
                    <p class='mb-1'><strong>البريد:</strong> <EMAIL></p>
                    <p class='mb-3'><strong>كلمة المرور:</strong> requester123</p>
                    
                    <h6 class='text-primary'>محطة وقود</h6>
                    <p class='mb-1'><strong>البريد:</strong> <EMAIL></p>
                    <p class='mb-3'><strong>كلمة المرور:</strong> station123</p>
                </div>
            </div>
          </div>";

    echo "<div class='text-center mt-4'>
            <div class='alert alert-success'>
                <h4><i class='fas fa-check-circle me-2'></i>تم إعداد النظام بنجاح!</h4>
                <p class='mb-3'>يمكنك الآن الوصول إلى النظام واستخدام جميع المميزات</p>
                <a href='index.php' class='btn btn-primary btn-lg'>
                    <i class='fas fa-sign-in-alt me-2'></i>
                    الدخول إلى النظام
                </a>
            </div>
          </div>";

} catch (Exception $e) {
    echo "<div class='step error'>
            <h5><i class='fas fa-exclamation-triangle me-2'></i>حدث خطأ أثناء الإعداد</h5>
            <p class='text-danger'>" . htmlspecialchars($e->getMessage()) . "</p>
            <p class='text-muted'>يرجى التأكد من صلاحيات الملفات والمجلدات</p>
          </div>";
}

echo "                </div>
            </div>
        </div>
    </div>
    <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
