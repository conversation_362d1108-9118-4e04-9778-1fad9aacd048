<?php
// ملف index مبسط للاختبار
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>نظام صرف حصص الوقود الذكي</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .welcome-card { background: rgba(255, 255, 255, 0.95); border-radius: 20px; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1); }
    </style>
</head>
<body>
    <div class='container mt-5'>
        <div class='row justify-content-center'>
            <div class='col-md-8'>
                <div class='welcome-card p-5 text-center'>
                    <i class='fas fa-gas-pump fa-5x text-primary mb-4'></i>
                    <h1 class='text-primary mb-3'>نظام صرف حصص الوقود الذكي</h1>
                    <p class='lead text-muted mb-4'>وزارة المالية والتخطيط الاقتصادي - ولاية الخرطوم</p>
                    
                    <div class='row mb-4'>
                        <div class='col-md-6'>
                            <div class='card h-100'>
                                <div class='card-body'>
                                    <h5 class='card-title text-success'>
                                        <i class='fas fa-check-circle me-2'></i>
                                        النظام جاهز
                                    </h5>
                                    <p class='card-text'>تم إعداد النظام بنجاح وهو جاهز للاستخدام</p>
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6'>
                            <div class='card h-100'>
                                <div class='card-body'>
                                    <h5 class='card-title text-info'>
                                        <i class='fas fa-database me-2'></i>
                                        قاعدة البيانات
                                    </h5>
                                    <p class='card-text'>تم إنشاء قاعدة البيانات مع البيانات التجريبية</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class='d-grid gap-2 d-md-flex justify-content-md-center'>
                        <a href='views/auth/login.php' class='btn btn-primary btn-lg me-md-2'>
                            <i class='fas fa-sign-in-alt me-2'></i>
                            تسجيل الدخول
                        </a>
                        <a href='setup.php' class='btn btn-secondary btn-lg'>
                            <i class='fas fa-cog me-2'></i>
                            إعادة الإعداد
                        </a>
                    </div>
                    
                    <hr class='my-4'>
                    
                    <div class='row text-start'>
                        <div class='col-md-6'>
                            <h6 class='text-primary'>بيانات تسجيل الدخول:</h6>
                            <small class='text-muted'>
                                <strong>مدير النظام:</strong><br>
                                البريد: <EMAIL><br>
                                كلمة المرور: admin123<br><br>
                                
                                <strong>مشرف:</strong><br>
                                البريد: <EMAIL><br>
                                كلمة المرور: supervisor123
                            </small>
                        </div>
                        <div class='col-md-6'>
                            <h6 class='text-primary'>المميزات الرئيسية:</h6>
                            <small class='text-muted'>
                                • إدارة المركبات والمحطات<br>
                                • نظام طلبات الوقود الذكي<br>
                                • توليد الخطابات الرسمية<br>
                                • التقارير والإحصائيات<br>
                                • نظام الحصص المتقدم
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";

// معلومات تشخيصية
echo "<!-- معلومات تشخيصية:
PHP Version: " . phpversion() . "
Current Directory: " . __DIR__ . "
Request URI: " . $_SERVER['REQUEST_URI'] . "
-->";
?>
