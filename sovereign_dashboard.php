<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// التحقق من تسجيل الدخول والصلاحيات السيادية
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['user_role'], ['minister', 'director_general', 'auditor'])) {
    header('Location: login.php');
    exit;
}

// تحديد المسارات
define('ROOT_PATH', __DIR__);
define('CONFIG_PATH', ROOT_PATH . '/config');

// تحميل الإعدادات السيادية
require_once CONFIG_PATH . '/database.php';
require_once CONFIG_PATH . '/sovereign.php';
require_once 'models/SovereignQuota.php';
require_once 'models/SovereignSignature.php';

$user = [
    'id' => $_SESSION['user_id'],
    'name' => $_SESSION['user_name'],
    'role' => $_SESSION['user_role'],
    'email' => $_SESSION['user_email']
];

$sovereignConfig = require CONFIG_PATH . '/sovereign.php';
$quotaManager = new SovereignQuota();
$signatureManager = new SovereignSignature();

// الحصول على الإحصائيات السيادية
try {
    $db = Database::getInstance()->getConnection();
    
    // إحصائيات اليوم
    $stmt = $db->prepare("
        SELECT 
            COUNT(*) as total_requests,
            SUM(CASE WHEN status = 'approved' THEN quantity ELSE 0 END) as approved_fuel,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_requests,
            COUNT(CASE WHEN status = 'emergency' THEN 1 END) as emergency_requests
        FROM fuel_requests 
        WHERE DATE(request_date) = DATE('now')
    ");
    $stmt->execute();
    $todayStats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // إحصائيات الوزارات
    $stmt = $db->prepare("
        SELECT 
            ministry_code,
            ministry_name,
            allocated_amount,
            consumed_amount,
            remaining_amount,
            (consumed_amount * 100.0 / allocated_amount) as utilization_rate
        FROM ministry_quotas 
        WHERE year = ? AND month = ?
        ORDER BY utilization_rate DESC
    ");
    $stmt->execute([date('Y'), date('n')]);
    $ministryStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات التوقيع الرقمي
    $signatureStats = $signatureManager->getSovereignStatistics('monthly');
    
    // المحطات الأكثر نشاطاً
    $stmt = $db->prepare("
        SELECT 
            s.name,
            s.location,
            COUNT(fr.id) as total_requests,
            SUM(fr.quantity) as total_fuel
        FROM stations s
        LEFT JOIN fuel_requests fr ON s.id = fr.station_id 
            AND DATE(fr.request_date) >= DATE('now', 'start of month')
        WHERE s.is_active = 1
        GROUP BY s.id
        ORDER BY total_fuel DESC
        LIMIT 10
    ");
    $stmt->execute();
    $topStations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // التنبيهات السيادية
    $alerts = [];
    
    // تحقق من تجاوز الحصص
    foreach ($ministryStats as $ministry) {
        if ($ministry['utilization_rate'] > 90) {
            $alerts[] = [
                'type' => 'quota_exceeded',
                'level' => 'high',
                'message' => $ministry['ministry_name'] . ' تجاوزت 90% من الحصة',
                'ministry' => $ministry['ministry_code']
            ];
        }
    }
    
    // تحقق من الطلبات الطارئة
    if ($todayStats['emergency_requests'] > 5) {
        $alerts[] = [
            'type' => 'emergency_spike',
            'level' => 'medium',
            'message' => 'ارتفاع في طلبات الطوارئ اليوم: ' . $todayStats['emergency_requests'],
            'count' => $todayStats['emergency_requests']
        ];
    }
    
} catch (Exception $e) {
    $error = $e->getMessage();
    $todayStats = ['total_requests' => 0, 'approved_fuel' => 0, 'pending_requests' => 0, 'emergency_requests' => 0];
    $ministryStats = [];
    $signatureStats = [];
    $topStations = [];
    $alerts = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم السيادية - نظام إدارة الوقود الحكومي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.0.0/dist/chart.min.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .sovereign-header {
            background: linear-gradient(135deg, #0f2027 0%, #203a43 50%, #2c5364 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        
        .sovereign-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .sovereign-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            transition: all 0.3s ease;
        }
        
        .stats-card.success { background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); }
        .stats-card.warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stats-card.info { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stats-card.danger { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        
        .ministry-progress {
            height: 8px;
            border-radius: 10px;
            background: #e9ecef;
            overflow: hidden;
        }
        
        .ministry-progress .progress-bar {
            border-radius: 10px;
            transition: width 0.6s ease;
        }
        
        .alert-item {
            border-left: 4px solid;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        
        .alert-high { border-left-color: #dc3545; background: rgba(220, 53, 69, 0.1); }
        .alert-medium { border-left-color: #ffc107; background: rgba(255, 193, 7, 0.1); }
        .alert-low { border-left-color: #17a2b8; background: rgba(23, 162, 184, 0.1); }
        
        .coat-of-arms {
            width: 60px;
            height: 60px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="45" fill="%23FFD700" stroke="%23000" stroke-width="2"/><text x="50" y="55" text-anchor="middle" font-size="12" fill="%23000">السودان</text></svg>') center/contain no-repeat;
        }
        
        .real-time-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #28a745;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- الرأس السيادي -->
    <div class="sovereign-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-2 text-center">
                    <div class="coat-of-arms mx-auto"></div>
                </div>
                <div class="col-md-8 text-center">
                    <h2 class="mb-1"><?= htmlspecialchars($sovereignConfig['sovereign_info']['country']) ?></h2>
                    <h4 class="mb-1"><?= htmlspecialchars($sovereignConfig['sovereign_info']['ministry']) ?></h4>
                    <p class="mb-0"><?= htmlspecialchars($sovereignConfig['sovereign_info']['department']) ?></p>
                </div>
                <div class="col-md-2 text-end">
                    <div class="dropdown">
                        <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i>
                            <?= htmlspecialchars($user['name']) ?>
                        </button>
                        <ul class="dropdown-menu">
                            <li><span class="dropdown-item-text">الدور: <?= htmlspecialchars($user['role']) ?></span></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid mt-4">
        <!-- مؤشرات الأداء الرئيسية -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="sovereign-card p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">
                            <i class="fas fa-tachometer-alt me-2 text-primary"></i>
                            مؤشرات الأداء السيادية
                            <span class="real-time-indicator ms-2"></span>
                        </h5>
                        <small class="text-muted">آخر تحديث: <?= date('Y-m-d H:i:s') ?></small>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stats-card p-3 text-center">
                                <i class="fas fa-file-alt fa-2x mb-2"></i>
                                <h3 class="mb-1"><?= number_format($todayStats['total_requests']) ?></h3>
                                <p class="mb-0">طلبات اليوم</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card success p-3 text-center">
                                <i class="fas fa-gas-pump fa-2x mb-2"></i>
                                <h3 class="mb-1"><?= number_format($todayStats['approved_fuel']) ?></h3>
                                <p class="mb-0">لتر معتمد اليوم</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card warning p-3 text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h3 class="mb-1"><?= number_format($todayStats['pending_requests']) ?></h3>
                                <p class="mb-0">طلبات معلقة</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card danger p-3 text-center">
                                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                                <h3 class="mb-1"><?= number_format($todayStats['emergency_requests']) ?></h3>
                                <p class="mb-0">طلبات طوارئ</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- حالة الحصص الوزارية -->
            <div class="col-md-8">
                <div class="sovereign-card p-4 mb-4">
                    <h5 class="mb-3">
                        <i class="fas fa-building me-2 text-primary"></i>
                        حالة الحصص الوزارية - <?= date('F Y') ?>
                    </h5>
                    
                    <?php if (!empty($ministryStats)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الوزارة</th>
                                        <th>المخصص</th>
                                        <th>المستهلك</th>
                                        <th>المتبقي</th>
                                        <th>نسبة الاستخدام</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($ministryStats as $ministry): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($ministry['ministry_name']) ?></strong>
                                        </td>
                                        <td><?= number_format($ministry['allocated_amount']) ?> لتر</td>
                                        <td><?= number_format($ministry['consumed_amount']) ?> لتر</td>
                                        <td><?= number_format($ministry['remaining_amount']) ?> لتر</td>
                                        <td>
                                            <div class="ministry-progress">
                                                <div class="progress-bar <?= $ministry['utilization_rate'] > 90 ? 'bg-danger' : ($ministry['utilization_rate'] > 70 ? 'bg-warning' : 'bg-success') ?>" 
                                                     style="width: <?= min($ministry['utilization_rate'], 100) ?>%"></div>
                                            </div>
                                            <small><?= number_format($ministry['utilization_rate'], 1) ?>%</small>
                                        </td>
                                        <td>
                                            <?php if ($ministry['utilization_rate'] > 90): ?>
                                                <span class="badge bg-danger">تجاوز</span>
                                            <?php elseif ($ministry['utilization_rate'] > 70): ?>
                                                <span class="badge bg-warning">تحذير</span>
                                            <?php else: ?>
                                                <span class="badge bg-success">طبيعي</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد بيانات حصص متاحة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- التنبيهات السيادية -->
            <div class="col-md-4">
                <div class="sovereign-card p-4 mb-4">
                    <h5 class="mb-3">
                        <i class="fas fa-bell me-2 text-warning"></i>
                        التنبيهات السيادية
                    </h5>
                    
                    <?php if (!empty($alerts)): ?>
                        <?php foreach ($alerts as $alert): ?>
                        <div class="alert-item alert-<?= $alert['level'] ?> p-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <div>
                                    <strong><?= htmlspecialchars($alert['message']) ?></strong>
                                    <br><small class="text-muted"><?= date('H:i') ?></small>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <p class="text-muted">لا توجد تنبيهات</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- إحصائيات التوقيع الرقمي -->
                <div class="sovereign-card p-4">
                    <h5 class="mb-3">
                        <i class="fas fa-signature me-2 text-info"></i>
                        التوقيع الرقمي السيادي
                    </h5>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary"><?= $signatureStats['total_signatures'] ?? 0 ?></h4>
                            <small>إجمالي التوقيعات</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success"><?= $signatureStats['active_signatures'] ?? 0 ?></h4>
                            <small>توقيعات نشطة</small>
                        </div>
                        <div class="col-6 mt-2">
                            <h4 class="text-warning"><?= $signatureStats['ministerial_signatures'] ?? 0 ?></h4>
                            <small>توقيعات وزارية</small>
                        </div>
                        <div class="col-6 mt-2">
                            <h4 class="text-info"><?= $signatureStats['directorial_signatures'] ?? 0 ?></h4>
                            <small>توقيعات إدارية</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- المحطات الأكثر نشاطاً -->
        <div class="row">
            <div class="col-12">
                <div class="sovereign-card p-4">
                    <h5 class="mb-3">
                        <i class="fas fa-gas-pump me-2 text-success"></i>
                        المحطات الأكثر نشاطاً - الشهر الحالي
                    </h5>
                    
                    <?php if (!empty($topStations)): ?>
                        <div class="row">
                            <?php foreach (array_slice($topStations, 0, 5) as $station): ?>
                            <div class="col-md-2-4">
                                <div class="card border-0 bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-gas-pump fa-2x text-primary mb-2"></i>
                                        <h6 class="card-title"><?= htmlspecialchars($station['name']) ?></h6>
                                        <p class="card-text">
                                            <small class="text-muted"><?= htmlspecialchars($station['location']) ?></small><br>
                                            <strong><?= number_format($station['total_fuel'] ?? 0) ?> لتر</strong><br>
                                            <small><?= $station['total_requests'] ?? 0 ?> طلب</small>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-gas-pump fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد بيانات محطات متاحة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.0.0/dist/chart.min.js"></script>
    <script>
        // تحديث البيانات كل 30 ثانية
        setInterval(function() {
            location.reload();
        }, 30000);
        
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            // تحريك شريط التقدم
            const progressBars = document.querySelectorAll('.progress-bar');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 500);
            });
        });
    </script>
</body>
</html>
