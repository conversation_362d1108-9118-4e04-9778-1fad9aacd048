<?php
/**
 * إعداد النظام السيادي المتكامل
 * وزارة المالية والتخطيط الاقتصادي - جمهورية السودان
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// تحديد المسارات
define('ROOT_PATH', __DIR__);
define('CONFIG_PATH', ROOT_PATH . '/config');

// تحميل إعدادات قاعدة البيانات
require_once CONFIG_PATH . '/database.php';
require_once CONFIG_PATH . '/sovereign.php';

$sovereignConfig = require CONFIG_PATH . '/sovereign.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعداد النظام السيادي - نظام إدارة الوقود الحكومي</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { 
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); 
            min-height: 100vh; 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .setup-card { 
            background: rgba(255, 255, 255, 0.95); 
            border-radius: 20px; 
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        .step { 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 10px; 
            border-left: 4px solid;
        }
        .step.success { 
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); 
            border-left-color: #28a745; 
        }
        .step.error { 
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); 
            border-left-color: #dc3545; 
        }
        .step.info { 
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); 
            border-left-color: #17a2b8; 
        }
        .step.warning { 
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); 
            border-left-color: #ffc107; 
        }
        .coat-of-arms {
            width: 100px;
            height: 100px;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"50\" cy=\"50\" r=\"45\" fill=\"%23FFD700\" stroke=\"%23000\" stroke-width=\"2\"/><text x=\"50\" y=\"55\" text-anchor=\"middle\" font-size=\"10\" fill=\"%23000\">السودان</text></svg>') center/contain no-repeat;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <div class='container mt-5'>
        <div class='row justify-content-center'>
            <div class='col-md-10'>
                <div class='setup-card p-4'>
                    <div class='text-center mb-4'>
                        <div class='coat-of-arms mb-3'></div>
                        <h2 class='text-primary'>" . htmlspecialchars($sovereignConfig['sovereign_info']['country']) . "</h2>
                        <h4 class='text-secondary'>" . htmlspecialchars($sovereignConfig['sovereign_info']['ministry']) . "</h4>
                        <p class='text-muted'>إعداد النظام السيادي المتكامل لإدارة الوقود الحكومي</p>
                    </div>";

try {
    // الخطوة 1: التحقق من البيئة
    echo "<div class='step info'>
            <h5><i class='fas fa-server me-2'></i>الخطوة 1: فحص البيئة التشغيلية</h5>";
    
    $phpVersion = phpversion();
    $requiredVersion = '7.4.0';
    
    if (version_compare($phpVersion, $requiredVersion, '>=')) {
        echo "<p class='text-success mb-1'><i class='fas fa-check me-2'></i>إصدار PHP: $phpVersion ✓</p>";
    } else {
        echo "<p class='text-danger mb-1'><i class='fas fa-times me-2'></i>إصدار PHP غير متوافق: $phpVersion (مطلوب: $requiredVersion+)</p>";
    }
    
    // فحص الإضافات المطلوبة
    $requiredExtensions = ['pdo', 'pdo_sqlite', 'openssl', 'json', 'mbstring'];
    foreach ($requiredExtensions as $ext) {
        if (extension_loaded($ext)) {
            echo "<p class='text-success mb-1'><i class='fas fa-check me-2'></i>إضافة $ext: متوفرة ✓</p>";
        } else {
            echo "<p class='text-danger mb-1'><i class='fas fa-times me-2'></i>إضافة $ext: غير متوفرة ✗</p>";
        }
    }
    
    echo "</div>";

    // الخطوة 2: إنشاء قاعدة البيانات السيادية
    echo "<div class='step info'>
            <h5><i class='fas fa-database me-2'></i>الخطوة 2: إعداد قاعدة البيانات السيادية</h5>";
    
    $db = Database::getInstance();
    echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم إنشاء قاعدة البيانات السيادية بنجاح</p>";
    echo "</div>";

    // الخطوة 3: إدراج البيانات السيادية
    echo "<div class='step info'>
            <h5><i class='fas fa-crown me-2'></i>الخطوة 3: إدراج البيانات السيادية</h5>";
    
    $connection = $db->getConnection();
    
    // إضافة المستخدمين السياديين
    $sovereignUsers = [
        ['معالي وزير المالية', '<EMAIL>', 'minister2025', 'minister', null],
        ['المدير العام للوقود', '<EMAIL>', 'director2025', 'director_general', null],
        ['مدير التدقيق السيادي', '<EMAIL>', 'auditor2025', 'auditor', null],
        ['مشرف النظام السيادي', '<EMAIL>', 'sovereign2025', 'supervisor', null]
    ];
    
    foreach ($sovereignUsers as $user) {
        $hashedPassword = password_hash($user[2], PASSWORD_DEFAULT);
        $stmt = $connection->prepare("
            INSERT OR IGNORE INTO users (name, email, password, role, station_id) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$user[0], $user[1], $hashedPassword, $user[3], $user[4]]);
    }
    echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم إدراج المستخدمين السياديين</p>";
    
    // إضافة الحصص الوزارية للشهر الحالي
    $ministryAllocations = $sovereignConfig['quota_policies']['ministry_allocations'];
    $currentYear = date('Y');
    $currentMonth = date('n');
    
    foreach ($ministryAllocations as $code => $allocation) {
        $ministryNames = [
            'finance' => 'وزارة المالية والتخطيط الاقتصادي',
            'interior' => 'وزارة الداخلية',
            'defense' => 'وزارة الدفاع',
            'health' => 'وزارة الصحة',
            'education' => 'وزارة التعليم',
            'transport' => 'وزارة النقل',
            'energy' => 'وزارة الطاقة والتعدين',
            'agriculture' => 'وزارة الزراعة والغابات',
            'water' => 'وزارة الموارد المائية والري',
            'telecommunications' => 'وزارة الاتصالات وتقنية المعلومات'
        ];
        
        $stmt = $connection->prepare("
            INSERT OR REPLACE INTO ministry_quotas (
                ministry_code, ministry_name, year, month, 
                allocated_amount, consumed_amount, remaining_amount, status
            ) VALUES (?, ?, ?, ?, ?, 0, ?, 'active')
        ");
        
        $stmt->execute([
            $code,
            $ministryNames[$code],
            $currentYear,
            $currentMonth,
            $allocation,
            $allocation
        ]);
    }
    echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم تخصيص الحصص الوزارية للشهر الحالي</p>";
    
    // إضافة مركبات سيادية تجريبية
    $sovereignVehicles = [
        ['س و 0001', 'GOV001MINISTER', 'مكتب معالي الوزير', 'سيارة تنفيذية', 'gasoline', 80, 'finance', 'executive', 800],
        ['س و 0002', 'GOV002DIRECTOR', 'مكتب المدير العام', 'سيارة إدارية', 'gasoline', 60, 'finance', 'executive', 600],
        ['س و 1001', 'INT001PATROL', 'وزارة الداخلية', 'سيارة دورية', 'gasoline', 70, 'interior', 'service', 500],
        ['س و 2001', 'DEF001TRANS', 'وزارة الدفاع', 'شاحنة نقل', 'diesel', 300, 'defense', 'transport', 1200],
        ['س و 3001', 'HLT001AMB', 'وزارة الصحة', 'سيارة إسعاف', 'gasoline', 60, 'health', 'emergency', 2000]
    ];
    
    foreach ($sovereignVehicles as $vehicle) {
        $stmt = $connection->prepare("
            INSERT OR IGNORE INTO vehicles (
                plate_number, chasis_number, owner, vehicle_type, fuel_type, 
                tank_capacity, ministry_code, vehicle_category, monthly_quota
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute($vehicle);
    }
    echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم إدراج المركبات السيادية التجريبية</p>";
    
    echo "</div>";

    // الخطوة 4: إعداد الأمان السيادي
    echo "<div class='step warning'>
            <h5><i class='fas fa-shield-alt me-2'></i>الخطوة 4: تفعيل الأمان السيادي</h5>";
    
    // إنشاء مفاتيح التشفير السيادية
    $encryptionKey = hash('sha256', 'SUDAN_SOVEREIGN_ENCRYPTION_2025_' . time());
    $signatureKey = hash('sha256', 'SUDAN_SOVEREIGN_SIGNATURE_2025_' . time());
    
    echo "<p class='text-success'><i class='fas fa-key me-2'></i>تم توليد مفاتيح التشفير السيادية</p>";
    echo "<p class='text-info'><i class='fas fa-info-circle me-2'></i>مفتاح التشفير: " . substr($encryptionKey, 0, 16) . "...</p>";
    echo "<p class='text-info'><i class='fas fa-info-circle me-2'></i>مفتاح التوقيع: " . substr($signatureKey, 0, 16) . "...</p>";
    
    // تسجيل إعداد النظام في سجل التدقيق
    $stmt = $connection->prepare("
        INSERT INTO sovereign_audit_log (action, details, user_id, ip_address, timestamp) 
        VALUES (?, ?, ?, ?, ?)
    ");
    
    $setupDetails = json_encode([
        'action' => 'sovereign_system_setup',
        'version' => '1.0.0',
        'ministries_configured' => count($ministryAllocations),
        'vehicles_added' => count($sovereignVehicles),
        'users_created' => count($sovereignUsers),
        'encryption_enabled' => true,
        'digital_signature_enabled' => true
    ]);
    
    $stmt->execute([
        'sovereign_system_setup',
        $setupDetails,
        null,
        $_SERVER['REMOTE_ADDR'] ?? 'localhost',
        date('Y-m-d H:i:s')
    ]);
    
    echo "<p class='text-success'><i class='fas fa-check me-2'></i>تم تسجيل إعداد النظام في سجل التدقيق السيادي</p>";
    echo "</div>";

    // الخطوة 5: التحقق من النظام
    echo "<div class='step success'>
            <h5><i class='fas fa-check-circle me-2'></i>الخطوة 5: التحقق من النظام السيادي</h5>";
    
    // إحصائيات النظام
    $stats = [];
    
    $stmt = $connection->query("SELECT COUNT(*) FROM users WHERE role IN ('minister', 'director_general', 'auditor')");
    $stats['sovereign_users'] = $stmt->fetchColumn();
    
    $stmt = $connection->query("SELECT COUNT(*) FROM ministry_quotas WHERE year = $currentYear AND month = $currentMonth");
    $stats['ministry_quotas'] = $stmt->fetchColumn();
    
    $stmt = $connection->query("SELECT COUNT(*) FROM vehicles WHERE ministry_code IS NOT NULL");
    $stats['sovereign_vehicles'] = $stmt->fetchColumn();
    
    $stmt = $connection->query("SELECT COUNT(*) FROM stations WHERE is_active = 1");
    $stats['active_stations'] = $stmt->fetchColumn();
    
    foreach ($stats as $key => $value) {
        $labels = [
            'sovereign_users' => 'المستخدمون السياديون',
            'ministry_quotas' => 'الحصص الوزارية',
            'sovereign_vehicles' => 'المركبات السيادية',
            'active_stations' => 'المحطات النشطة'
        ];
        echo "<p class='mb-1'><strong>{$labels[$key]}:</strong> $value</p>";
    }
    
    echo "</div>";

    // معلومات تسجيل الدخول السيادية
    echo "<div class='step success'>
            <h5><i class='fas fa-crown me-2'></i>بيانات تسجيل الدخول السيادية</h5>
            <div class='row'>
                <div class='col-md-6'>
                    <h6 class='text-primary'>المستوى الوزاري</h6>
                    <p class='mb-1'><strong>معالي الوزير:</strong></p>
                    <p class='mb-1'>البريد: <EMAIL></p>
                    <p class='mb-3'>كلمة المرور: minister2025</p>
                    
                    <h6 class='text-primary'>المستوى الإداري</h6>
                    <p class='mb-1'><strong>المدير العام:</strong></p>
                    <p class='mb-1'>البريد: <EMAIL></p>
                    <p class='mb-3'>كلمة المرور: director2025</p>
                </div>
                <div class='col-md-6'>
                    <h6 class='text-primary'>مستوى التدقيق</h6>
                    <p class='mb-1'><strong>مدير التدقيق:</strong></p>
                    <p class='mb-1'>البريد: <EMAIL></p>
                    <p class='mb-3'>كلمة المرور: auditor2025</p>
                    
                    <h6 class='text-primary'>مستوى الإشراف</h6>
                    <p class='mb-1'><strong>مشرف النظام:</strong></p>
                    <p class='mb-1'>البريد: <EMAIL></p>
                    <p class='mb-3'>كلمة المرور: sovereign2025</p>
                </div>
            </div>
          </div>";

    echo "<div class='text-center mt-4'>
            <div class='alert alert-success'>
                <h4><i class='fas fa-crown me-2'></i>تم إعداد النظام السيادي بنجاح!</h4>
                <p class='mb-3'>النظام جاهز للعمل بجميع المميزات السيادية المتقدمة</p>
                <div class='d-grid gap-2 d-md-flex justify-content-md-center'>
                    <a href='sovereign_dashboard.php' class='btn btn-primary btn-lg me-md-2'>
                        <i class='fas fa-tachometer-alt me-2'></i>
                        لوحة التحكم السيادية
                    </a>
                    <a href='login.php' class='btn btn-success btn-lg me-md-2'>
                        <i class='fas fa-sign-in-alt me-2'></i>
                        تسجيل الدخول
                    </a>
                    <a href='verify_letter.php' class='btn btn-info btn-lg'>
                        <i class='fas fa-qrcode me-2'></i>
                        التحقق من الخطابات
                    </a>
                </div>
            </div>
          </div>";

} catch (Exception $e) {
    echo "<div class='step error'>
            <h5><i class='fas fa-exclamation-triangle me-2'></i>حدث خطأ أثناء الإعداد</h5>
            <p class='text-danger'>" . htmlspecialchars($e->getMessage()) . "</p>
            <p class='text-muted'>يرجى التأكد من صلاحيات الملفات والمجلدات</p>
          </div>";
}

echo "                </div>
            </div>
        </div>
    </div>
    <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";
?>
