<?php
// ملف اختبار بسيط
echo "PHP يعمل بشكل صحيح!<br>";
echo "إصدار PHP: " . phpversion() . "<br>";
echo "المسار الحالي: " . __DIR__ . "<br>";

// اختبار قاعدة البيانات
try {
    $db = new PDO('sqlite:' . __DIR__ . '/database/fuel_system.db');
    echo "قاعدة البيانات متصلة بنجاح!<br>";
    
    $stmt = $db->query("SELECT COUNT(*) FROM users");
    $count = $stmt->fetchColumn();
    echo "عدد المستخدمين: " . $count . "<br>";
    
} catch (Exception $e) {
    echo "خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
}

// اختبار الملفات
$files = [
    'config/app.php',
    'config/database.php',
    'models/User.php',
    'controllers/AuthController.php'
];

echo "<h3>فحص الملفات:</h3>";
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✅ $file موجود<br>";
    } else {
        echo "❌ $file غير موجود<br>";
    }
}
?>
