<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// تحديد المسارات
define('ROOT_PATH', __DIR__);
define('CONFIG_PATH', ROOT_PATH . '/config');

// تحميل قاعدة البيانات
require_once CONFIG_PATH . '/database.php';

$user = [
    'id' => $_SESSION['user_id'],
    'name' => $_SESSION['user_name'],
    'role' => $_SESSION['user_role'],
    'email' => $_SESSION['user_email']
];

$errors = [];
$success = '';

// معالجة إضافة مركبة جديدة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_vehicle'])) {
    $plateNumber = $_POST['plate_number'] ?? '';
    $chasisNumber = $_POST['chasis_number'] ?? '';
    $owner = $_POST['owner'] ?? '';
    $vehicleType = $_POST['vehicle_type'] ?? '';
    $fuelType = $_POST['fuel_type'] ?? '';
    $tankCapacity = $_POST['tank_capacity'] ?? '';
    
    // التحقق من البيانات
    if (empty($plateNumber)) {
        $errors[] = 'رقم اللوحة مطلوب';
    }
    
    if (empty($chasisNumber)) {
        $errors[] = 'رقم الشاسيه مطلوب';
    }
    
    if (empty($owner)) {
        $errors[] = 'الجهة المالكة مطلوبة';
    }
    
    if (empty($vehicleType)) {
        $errors[] = 'نوع المركبة مطلوب';
    }
    
    if (empty($fuelType)) {
        $errors[] = 'نوع الوقود مطلوب';
    }
    
    if (empty($tankCapacity) || !is_numeric($tankCapacity) || $tankCapacity <= 0) {
        $errors[] = 'سعة الخزان يجب أن تكون رقماً موجباً';
    }
    
    if (empty($errors)) {
        try {
            $db = Database::getInstance()->getConnection();
            
            // التحقق من عدم تكرار رقم اللوحة
            $stmt = $db->prepare("SELECT COUNT(*) FROM vehicles WHERE plate_number = ?");
            $stmt->execute([$plateNumber]);
            if ($stmt->fetchColumn() > 0) {
                $errors[] = 'رقم اللوحة موجود مسبقاً';
            } else {
                // إضافة المركبة
                $stmt = $db->prepare("
                    INSERT INTO vehicles (plate_number, chasis_number, owner, vehicle_type, fuel_type, tank_capacity) 
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                
                $result = $stmt->execute([
                    $plateNumber,
                    $chasisNumber,
                    $owner,
                    $vehicleType,
                    $fuelType,
                    $tankCapacity
                ]);
                
                if ($result) {
                    $success = 'تم إضافة المركبة بنجاح!';
                    // مسح البيانات
                    $_POST = [];
                } else {
                    $errors[] = 'حدث خطأ أثناء إضافة المركبة';
                }
            }
        } catch (Exception $e) {
            $errors[] = 'حدث خطأ: ' . $e->getMessage();
        }
    }
}

// الحصول على المركبات
try {
    $db = Database::getInstance()->getConnection();
    $stmt = $db->prepare("SELECT * FROM vehicles WHERE is_active = 1 ORDER BY created_at DESC");
    $stmt->execute();
    $vehicles = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $vehicles = [];
}

$fuelTypes = [
    'gasoline' => 'جازولين',
    'diesel' => 'ديزل',
    'kerosene' => 'كيروسين'
];

$vehicleTypes = [
    'سيارة',
    'شاحنة',
    'حافلة',
    'دراجة نارية',
    'مركبة خاصة'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المركبات - نظام صرف حصص الوقود</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .table-card { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-gas-pump me-2"></i>
                نظام صرف حصص الوقود
            </a>
            
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-arrow-right me-1"></i>العودة للوحة التحكم
                </a>
            </div>
            
            <div class="navbar-nav">
                <span class="navbar-text">
                    <i class="fas fa-user me-1"></i>
                    <?= htmlspecialchars($user['name']) ?>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <h2 class="card-title mb-0">
                            <i class="fas fa-car me-2"></i>
                            إدارة المركبات
                        </h2>
                        <p class="card-text mt-2">إضافة وإدارة المركبات المسجلة في النظام</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- رسائل الأخطاء والنجاح -->
        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>يرجى تصحيح الأخطاء التالية:</strong>
                <ul class="mb-0 mt-2">
                    <?php foreach ($errors as $error): ?>
                        <li><?= htmlspecialchars($error) ?></li>
                    <?php endforeach; ?>
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($success) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- نموذج إضافة مركبة -->
            <div class="col-md-4">
                <div class="card table-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-plus me-2"></i>
                            إضافة مركبة جديدة
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="plate_number" class="form-label">رقم اللوحة *</label>
                                <input type="text" class="form-control" id="plate_number" name="plate_number" 
                                       value="<?= htmlspecialchars($_POST['plate_number'] ?? '') ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="chasis_number" class="form-label">رقم الشاسيه *</label>
                                <input type="text" class="form-control" id="chasis_number" name="chasis_number" 
                                       value="<?= htmlspecialchars($_POST['chasis_number'] ?? '') ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="owner" class="form-label">الجهة المالكة *</label>
                                <input type="text" class="form-control" id="owner" name="owner" 
                                       value="<?= htmlspecialchars($_POST['owner'] ?? '') ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="vehicle_type" class="form-label">نوع المركبة *</label>
                                <select class="form-select" id="vehicle_type" name="vehicle_type" required>
                                    <option value="">اختر نوع المركبة</option>
                                    <?php foreach ($vehicleTypes as $type): ?>
                                        <option value="<?= $type ?>" <?= (($_POST['vehicle_type'] ?? '') === $type) ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($type) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="fuel_type" class="form-label">نوع الوقود *</label>
                                <select class="form-select" id="fuel_type" name="fuel_type" required>
                                    <option value="">اختر نوع الوقود</option>
                                    <?php foreach ($fuelTypes as $key => $name): ?>
                                        <option value="<?= $key ?>" <?= (($_POST['fuel_type'] ?? '') === $key) ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($name) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="tank_capacity" class="form-label">سعة الخزان (لتر) *</label>
                                <input type="number" class="form-control" id="tank_capacity" name="tank_capacity" 
                                       min="1" value="<?= htmlspecialchars($_POST['tank_capacity'] ?? '') ?>" required>
                            </div>
                            
                            <button type="submit" name="add_vehicle" class="btn btn-success w-100">
                                <i class="fas fa-plus me-2"></i>
                                إضافة المركبة
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- قائمة المركبات -->
            <div class="col-md-8">
                <div class="card table-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            المركبات المسجلة (<?= count($vehicles) ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($vehicles)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>رقم اللوحة</th>
                                            <th>الجهة المالكة</th>
                                            <th>نوع المركبة</th>
                                            <th>نوع الوقود</th>
                                            <th>سعة الخزان</th>
                                            <th>تاريخ التسجيل</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($vehicles as $vehicle): ?>
                                        <tr>
                                            <td><strong><?= htmlspecialchars($vehicle['plate_number']) ?></strong></td>
                                            <td><?= htmlspecialchars($vehicle['owner']) ?></td>
                                            <td><?= htmlspecialchars($vehicle['vehicle_type']) ?></td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?= $fuelTypes[$vehicle['fuel_type']] ?? $vehicle['fuel_type'] ?>
                                                </span>
                                            </td>
                                            <td><?= $vehicle['tank_capacity'] ?> لتر</td>
                                            <td><?= date('Y-m-d', strtotime($vehicle['created_at'])) ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-car fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد مركبات مسجلة حتى الآن</p>
                                <p class="text-muted">استخدم النموذج على اليسار لإضافة مركبة جديدة</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
