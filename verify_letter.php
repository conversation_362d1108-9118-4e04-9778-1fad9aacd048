<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// تحديد المسارات
define('ROOT_PATH', __DIR__);
define('CONFIG_PATH', ROOT_PATH . '/config');

// تحميل الإعدادات السيادية
require_once CONFIG_PATH . '/database.php';
require_once CONFIG_PATH . '/sovereign.php';
require_once 'models/SovereignSignature.php';

$sovereignConfig = require CONFIG_PATH . '/sovereign.php';
$signatureManager = new SovereignSignature();

$verificationResult = null;
$qrHash = $_GET['qr'] ?? $_POST['qr_hash'] ?? '';

// معالجة التحقق
if (!empty($qrHash)) {
    $verificationResult = $signatureManager->verifySovereignQR($qrHash);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التحقق من الخطابات الرسمية - نظام إدارة الوقود الحكومي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .verification-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .verification-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 800px;
            width: 100%;
        }
        
        .verification-header {
            background: linear-gradient(135deg, #0f2027 0%, #203a43 50%, #2c5364 100%);
            color: white;
            border-radius: 20px 20px 0 0;
            padding: 2rem;
            text-align: center;
        }
        
        .verification-body {
            padding: 2rem;
        }
        
        .coat-of-arms {
            width: 80px;
            height: 80px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="45" fill="%23FFD700" stroke="%23000" stroke-width="2"/><text x="50" y="55" text-anchor="middle" font-size="12" fill="%23000">السودان</text></svg>') center/contain no-repeat;
            margin: 0 auto 1rem;
        }
        
        .verification-result {
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
        }
        
        .verification-valid {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 2px solid #28a745;
            color: #155724;
        }
        
        .verification-invalid {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border: 2px solid #dc3545;
            color: #721c24;
        }
        
        .document-details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 1rem;
        }
        
        .security-features {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 1rem;
        }
        
        .qr-scanner {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            background: rgba(0, 123, 255, 0.05);
        }
    </style>
</head>
<body>
    <div class="verification-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-12">
                    <div class="verification-card">
                        <div class="verification-header">
                            <div class="coat-of-arms"></div>
                            <h2 class="mb-2"><?= htmlspecialchars($sovereignConfig['sovereign_info']['country']) ?></h2>
                            <h4 class="mb-2"><?= htmlspecialchars($sovereignConfig['sovereign_info']['ministry']) ?></h4>
                            <p class="mb-0">نظام التحقق من الخطابات الرسمية</p>
                        </div>
                        
                        <div class="verification-body">
                            <!-- نموذج التحقق -->
                            <form method="POST" action="" class="mb-4">
                                <div class="row">
                                    <div class="col-md-8">
                                        <label for="qr_hash" class="form-label">
                                            <i class="fas fa-qrcode me-2"></i>
                                            رمز التحقق أو رابط QR
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="qr_hash" 
                                               name="qr_hash" 
                                               placeholder="أدخل رمز التحقق أو امسح رمز QR"
                                               value="<?= htmlspecialchars($qrHash) ?>"
                                               required>
                                        <small class="form-text text-muted">
                                            يمكنك إدخال رمز التحقق يدوياً أو استخدام كاميرا الهاتف لمسح رمز QR
                                        </small>
                                    </div>
                                    <div class="col-md-4 d-flex align-items-end">
                                        <button type="submit" class="btn btn-primary w-100">
                                            <i class="fas fa-search me-2"></i>
                                            التحقق من الخطاب
                                        </button>
                                    </div>
                                </div>
                            </form>

                            <!-- ماسح QR -->
                            <div class="qr-scanner mb-4">
                                <i class="fas fa-camera fa-3x text-primary mb-3"></i>
                                <h5>مسح رمز QR</h5>
                                <p class="text-muted">استخدم كاميرا الهاتف لمسح رمز QR الموجود على الخطاب</p>
                                <button type="button" class="btn btn-outline-primary" onclick="startQRScanner()">
                                    <i class="fas fa-camera me-2"></i>
                                    تشغيل الكاميرا
                                </button>
                            </div>

                            <!-- نتيجة التحقق -->
                            <?php if ($verificationResult !== null): ?>
                                <?php if ($verificationResult['valid']): ?>
                                    <div class="verification-result verification-valid">
                                        <div class="text-center mb-3">
                                            <i class="fas fa-check-circle fa-4x text-success"></i>
                                            <h3 class="mt-3">✓ خطاب رسمي صحيح</h3>
                                            <p class="lead">تم التحقق من صحة الخطاب بنجاح</p>
                                        </div>

                                        <div class="document-details">
                                            <h5 class="mb-3">
                                                <i class="fas fa-file-alt me-2"></i>
                                                تفاصيل الخطاب
                                            </h5>
                                            
                                            <?php 
                                            $documentData = json_decode($verificationResult['data'], true);
                                            $documentInfo = $verificationResult['document_info'];
                                            ?>
                                            
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p><strong>نوع الوثيقة:</strong> خطاب صرف وقود</p>
                                                    <p><strong>الجهة المصدرة:</strong> <?= htmlspecialchars($documentData['issuing_authority'] ?? 'وزارة المالية') ?></p>
                                                    <p><strong>الدولة:</strong> <?= htmlspecialchars($documentData['country'] ?? 'جمهورية السودان') ?></p>
                                                </div>
                                                <div class="col-md-6">
                                                    <p><strong>تاريخ الإصدار:</strong> <?= date('Y-m-d H:i', $documentData['timestamp'] ?? time()) ?></p>
                                                    <p><strong>رقم الوثيقة:</strong> <?= htmlspecialchars($documentInfo['document_id']) ?></p>
                                                    <p><strong>تنتهي صلاحيتها:</strong> <?= date('Y-m-d H:i', strtotime($documentInfo['expires_at'])) ?></p>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="security-features">
                                            <h5 class="mb-3">
                                                <i class="fas fa-shield-alt me-2"></i>
                                                المميزات الأمنية
                                            </h5>
                                            
                                            <div class="row">
                                                <div class="col-md-4 text-center">
                                                    <i class="fas fa-signature fa-2x text-primary mb-2"></i>
                                                    <h6>توقيع رقمي</h6>
                                                    <small class="text-success">✓ صحيح</small>
                                                </div>
                                                <div class="col-md-4 text-center">
                                                    <i class="fas fa-lock fa-2x text-primary mb-2"></i>
                                                    <h6>تشفير متقدم</h6>
                                                    <small class="text-success">✓ AES-256</small>
                                                </div>
                                                <div class="col-md-4 text-center">
                                                    <i class="fas fa-certificate fa-2x text-primary mb-2"></i>
                                                    <h6>ختم سيادي</h6>
                                                    <small class="text-success">✓ معتمد</small>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="alert alert-info mt-3">
                                            <i class="fas fa-info-circle me-2"></i>
                                            <strong>ملاحظة:</strong> هذا الخطاب صادر رسمياً من وزارة المالية والتخطيط الاقتصادي 
                                            ويحمل الختم الرسمي والتوقيع الرقمي المعتمد.
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <div class="verification-result verification-invalid">
                                        <div class="text-center mb-3">
                                            <i class="fas fa-times-circle fa-4x text-danger"></i>
                                            <h3 class="mt-3">✗ خطاب غير صحيح</h3>
                                            <p class="lead"><?= htmlspecialchars($verificationResult['reason']) ?></p>
                                        </div>

                                        <div class="alert alert-danger">
                                            <h5>
                                                <i class="fas fa-exclamation-triangle me-2"></i>
                                                تحذير أمني
                                            </h5>
                                            <p class="mb-2">هذا الخطاب قد يكون:</p>
                                            <ul class="mb-0">
                                                <li>مزور أو محرف</li>
                                                <li>منتهي الصلاحية</li>
                                                <li>مستخدم مسبقاً</li>
                                                <li>غير صادر من الجهة الرسمية</li>
                                            </ul>
                                        </div>

                                        <div class="alert alert-warning">
                                            <i class="fas fa-phone me-2"></i>
                                            <strong>للتأكد:</strong> يرجى الاتصال بوزارة المالية على الرقم: +249-183-123456
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>

                            <!-- معلومات إضافية -->
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="card border-0 bg-light">
                                        <div class="card-body text-center">
                                            <i class="fas fa-mobile-alt fa-2x text-primary mb-3"></i>
                                            <h6>تطبيق الهاتف</h6>
                                            <p class="small text-muted">حمل تطبيق "وقود حكومي" للتحقق السريع</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-0 bg-light">
                                        <div class="card-body text-center">
                                            <i class="fas fa-headset fa-2x text-primary mb-3"></i>
                                            <h6>الدعم الفني</h6>
                                            <p class="small text-muted">للمساعدة: <EMAIL></p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- روابط مفيدة -->
                            <div class="text-center mt-4">
                                <a href="dashboard.php" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-home me-2"></i>
                                    العودة للنظام
                                </a>
                                <a href="verify_letter.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-redo me-2"></i>
                                    تحقق جديد
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <small class="text-white">
                            © 2025 وزارة المالية والتخطيط الاقتصادي - جمهورية السودان
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // دالة تشغيل ماسح QR (مبسطة)
        function startQRScanner() {
            // في التطبيق الحقيقي، سيتم استخدام مكتبة QR scanner
            alert('سيتم تفعيل ماسح QR في الإصدار النهائي');
            
            // محاكاة مسح QR
            const sampleQR = 'SAMPLE_QR_' + Math.random().toString(36).substr(2, 9);
            document.getElementById('qr_hash').value = sampleQR;
        }

        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // التركيز على حقل الإدخال
            document.getElementById('qr_hash').focus();
            
            // إضافة تأثيرات بصرية
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });

        // معالجة الرابط المباشر للتحقق
        const urlParams = new URLSearchParams(window.location.search);
        const qrParam = urlParams.get('qr');
        if (qrParam && !document.getElementById('qr_hash').value) {
            document.getElementById('qr_hash').value = qrParam;
            document.querySelector('form').submit();
        }
    </script>
</body>
</html>
