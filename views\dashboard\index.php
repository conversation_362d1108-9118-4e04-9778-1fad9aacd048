<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?? 'لوحة التحكم' ?> - نظام صرف حصص الوقود</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.0.0/dist/chart.min.css" rel="stylesheet">
    <link href="/DESL_2/assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/DESL_2/dashboard">
                <i class="fas fa-gas-pump me-2"></i>
                نظام صرف حصص الوقود
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/DESL_2/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                        </a>
                    </li>
                    
                    <?php if ($user['role'] === 'admin' || $user['role'] === 'supervisor'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="/DESL_2/stations">
                            <i class="fas fa-gas-pump me-1"></i>المحطات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/DESL_2/vehicles">
                            <i class="fas fa-car me-1"></i>المركبات
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <li class="nav-item">
                        <a class="nav-link" href="/DESL_2/fuel/request">
                            <i class="fas fa-plus-circle me-1"></i>طلب وقود
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link" href="/DESL_2/reports">
                            <i class="fas fa-chart-bar me-1"></i>التقارير
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            <?= htmlspecialchars($user['name']) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user-cog me-2"></i>الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/DESL_2/logout"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- رسائل النظام -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($_SESSION['success_message']) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php unset($_SESSION['success_message']); ?>
        <?php endif; ?>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">طلبات اليوم</h6>
                                <h3 class="mb-0" id="todayRequests">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-calendar-day fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">طلبات معلقة</h6>
                                <h3 class="mb-0" id="pendingRequests">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">وقود اليوم (جالون)</h6>
                                <h3 class="mb-0" id="todayFuel">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-gas-pump fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">محطات نشطة</h6>
                                <h3 class="mb-0" id="activeStations">-</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-map-marker-alt fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- المحتوى الرئيسي حسب دور المستخدم -->
            <?php if ($dashboard_type === 'admin'): ?>
                <?php include 'admin_dashboard.php'; ?>
            <?php elseif ($dashboard_type === 'requester'): ?>
                <?php include 'requester_dashboard.php'; ?>
            <?php elseif ($dashboard_type === 'station'): ?>
                <?php include 'station_dashboard.php'; ?>
            <?php elseif ($dashboard_type === 'reviewer'): ?>
                <?php include 'reviewer_dashboard.php'; ?>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.0.0/dist/chart.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="/DESL_2/assets/js/app.js"></script>
    
    <script>
        // تحديث الإحصائيات السريعة
        function updateQuickStats() {
            fetch('/DESL_2/dashboard/quick-stats')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('todayRequests').textContent = data.total_requests_today || 0;
                    document.getElementById('pendingRequests').textContent = data.pending_requests || 0;
                    document.getElementById('todayFuel').textContent = (data.total_fuel_today || 0).toLocaleString();
                    document.getElementById('activeStations').textContent = data.active_stations || 0;
                })
                .catch(error => console.error('خطأ في تحديث الإحصائيات:', error));
        }

        // تحديث الإحصائيات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateQuickStats();
            
            // تحديث كل 30 ثانية
            setInterval(updateQuickStats, 30000);
        });
    </script>
</body>
</html>
