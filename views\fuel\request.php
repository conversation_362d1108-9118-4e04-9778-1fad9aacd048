<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?? 'طلب صرف حصة وقود' ?> - نظام صرف حصص الوقود</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="/DESL_2/assets/css/style.css" rel="stylesheet">
    <style>
        .vehicle-info-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
            border-radius: 15px;
            transition: all 0.3s ease;
        }
        
        .vehicle-info-card.found {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-color: #28a745;
        }
        
        .quota-progress {
            height: 20px;
            border-radius: 10px;
        }
        
        .station-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .station-card:hover {
            border-color: #007bff;
            box-shadow: 0 4px 8px rgba(0,123,255,0.2);
        }
        
        .station-card.selected {
            border-color: #28a745;
            background-color: #f8fff9;
        }
        
        .fuel-type-option {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .fuel-type-option:hover {
            border-color: #007bff;
        }
        
        .fuel-type-option.selected {
            border-color: #28a745;
            background-color: #f8fff9;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/DESL_2/dashboard">
                <i class="fas fa-gas-pump me-2"></i>
                نظام صرف حصص الوقود
            </a>
            
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="/DESL_2/dashboard">
                    <i class="fas fa-arrow-right me-1"></i>العودة للوحة التحكم
                </a>
            </div>
            
            <div class="navbar-nav">
                <span class="navbar-text">
                    <i class="fas fa-user me-1"></i>
                    <?= htmlspecialchars($user['name']) ?>
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h2 class="card-title mb-0">
                            <i class="fas fa-plus-circle me-2"></i>
                            طلب صرف حصة وقود جديد
                        </h2>
                        <p class="card-text mt-2">املأ البيانات المطلوبة لإنشاء طلب صرف وقود</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- رسائل الأخطاء والنجاح -->
        <?php if (isset($errors) && !empty($errors)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>يرجى تصحيح الأخطاء التالية:</strong>
                <ul class="mb-0 mt-2">
                    <?php foreach ($errors as $error): ?>
                        <li><?= htmlspecialchars($error) ?></li>
                    <?php endforeach; ?>
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($_SESSION['success_message']) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php unset($_SESSION['success_message']); ?>
        <?php endif; ?>

        <form method="POST" action="/DESL_2/fuel/request" id="fuelRequestForm">
            <div class="row">
                <!-- بيانات المركبة -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-car me-2"></i>
                                بيانات المركبة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="plate_number" class="form-label">
                                    <i class="fas fa-id-card me-1"></i>
                                    رقم اللوحة *
                                </label>
                                <div class="input-group">
                                    <input type="text" 
                                           class="form-control" 
                                           id="plate_number" 
                                           name="plate_number" 
                                           placeholder="مثال: س و 1234"
                                           value="<?= htmlspecialchars($form_data['plate_number'] ?? '') ?>"
                                           required>
                                    <button class="btn btn-outline-primary" type="button" id="searchVehicle">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <small class="form-text text-muted">أدخل رقم لوحة المركبة واضغط بحث</small>
                            </div>

                            <!-- معلومات المركبة -->
                            <div id="vehicleInfo" class="vehicle-info-card p-3 mb-3" style="display: none;">
                                <h6 class="text-success mb-3">
                                    <i class="fas fa-check-circle me-2"></i>
                                    تم العثور على المركبة
                                </h6>
                                <div class="row">
                                    <div class="col-6">
                                        <strong>الجهة المالكة:</strong>
                                        <div id="vehicleOwner">-</div>
                                    </div>
                                    <div class="col-6">
                                        <strong>نوع المركبة:</strong>
                                        <div id="vehicleType">-</div>
                                    </div>
                                    <div class="col-6 mt-2">
                                        <strong>رقم الشاسيه:</strong>
                                        <div id="vehicleChasis">-</div>
                                    </div>
                                    <div class="col-6 mt-2">
                                        <strong>سعة الخزان:</strong>
                                        <div id="vehicleTank">-</div>
                                    </div>
                                </div>

                                <!-- حالة الحصة -->
                                <div class="mt-3">
                                    <strong>حالة الحصة الشهرية:</strong>
                                    <div class="progress quota-progress mt-2">
                                        <div class="progress-bar" id="quotaProgress" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <div class="d-flex justify-content-between mt-1">
                                        <small>المستهلك: <span id="quotaUsed">0</span> لتر</small>
                                        <small>المتبقي: <span id="quotaRemaining">0</span> لتر</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الطلب -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="fas fa-gas-pump me-2"></i>
                                تفاصيل الطلب
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- نوع الوقود -->
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-oil-can me-1"></i>
                                    نوع الوقود *
                                </label>
                                <div class="row">
                                    <?php foreach ($fuel_types as $key => $name): ?>
                                    <div class="col-4">
                                        <div class="fuel-type-option" data-fuel-type="<?= $key ?>">
                                            <i class="fas fa-gas-pump fa-2x mb-2 text-primary"></i>
                                            <div><?= htmlspecialchars($name) ?></div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                <input type="hidden" name="fuel_type" id="fuel_type" required>
                            </div>

                            <!-- الكمية -->
                            <div class="mb-3">
                                <label for="quantity" class="form-label">
                                    <i class="fas fa-tachometer-alt me-1"></i>
                                    الكمية المطلوبة (لتر) *
                                </label>
                                <input type="number" 
                                       class="form-control" 
                                       id="quantity" 
                                       name="quantity" 
                                       min="1" 
                                       max="1000"
                                       placeholder="أدخل الكمية بالليتر"
                                       value="<?= htmlspecialchars($form_data['quantity'] ?? '') ?>"
                                       required>
                                <div class="form-text">الحد الأقصى: 1000 لتر</div>
                            </div>

                            <!-- ملاحظات -->
                            <div class="mb-3">
                                <label for="notes" class="form-label">
                                    <i class="fas fa-sticky-note me-1"></i>
                                    ملاحظات إضافية
                                </label>
                                <textarea class="form-control" 
                                          id="notes" 
                                          name="notes" 
                                          rows="3"
                                          placeholder="أي ملاحظات إضافية..."><?= htmlspecialchars($form_data['notes'] ?? '') ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- اختيار المحطة -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        اختيار محطة الوقود
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row" id="stationsContainer">
                        <?php foreach ($stations as $station): ?>
                        <div class="col-md-4 mb-3">
                            <div class="station-card p-3" data-station-id="<?= $station['id'] ?>">
                                <h6 class="text-primary">
                                    <i class="fas fa-gas-pump me-2"></i>
                                    <?= htmlspecialchars($station['name']) ?>
                                </h6>
                                <p class="text-muted mb-2">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    <?= htmlspecialchars($station['location']) ?>
                                </p>
                                <small class="text-info">
                                    <i class="fas fa-code me-1"></i>
                                    كود: <?= htmlspecialchars($station['code']) ?>
                                </small>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <input type="hidden" name="station_id" id="station_id" required>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="card">
                <div class="card-body text-center">
                    <button type="submit" class="btn btn-primary btn-lg me-3" id="submitBtn" disabled>
                        <i class="fas fa-paper-plane me-2"></i>
                        إرسال الطلب
                    </button>
                    <a href="/DESL_2/dashboard" class="btn btn-secondary btn-lg">
                        <i class="fas fa-times me-2"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="/DESL_2/assets/js/fuel-request.js"></script>
</body>
</html>
